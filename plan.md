# Electron-Vite Migration & Implementation Plan

## 1. Objective
Rebuild the desktop application using a clean, modern, and secure electron-vite boilerplate (`desktop-new-app`), then migrate and refactor existing main components, ensuring best practices for security and performance.

---

## 2. Reference: New Project Structure (`desktop-new-app`)

```
desktop-new-app/
├── electron.vite.config.ts   # Central config for main, preload, renderer
├── tailwind.config.js        # Tailwind CSS config (with daisyUI plugin)
├── package.json              # Project metadata and scripts
├── src/
│   ├── main/                 # Main process (Electron entry)
│   │   └── index.ts
│   ├── preload/              # Preload scripts (contextBridge, IPC)
│   │   └── index.ts
│   └── renderer/             # Frontend (React + TS)
|       |──src
│       |  ├── App.tsx
│       |  ├── main.tsx
│       |  └── ...
|       |──assets/main.css    # Tailwind + daisyUI imports
|       |──index.html
├── resource/                   # Static assets
├── node_modules/
└── ...
```

- **Main process:** Handles window creation, IPC, and native Node.js logic.
- **Preload:** Exposes safe APIs to renderer via contextBridge.
- **Renderer:** React-based UI, communicates with main/preload via IPC. Uses Tailwind CSS and daisyUI for styling and prebuilt components.
- **Config:** All Electron/Vite config in `electron.vite.config.ts`.
- **Styling:** Tailwind CSS and daisyUI integrated for modern, beautiful UI with minimal config.

---

## 3. Backend Reference
- The backend (API server) remains in the root directory as `backend/`.
- The new desktop app will connect to this backend for authentication and HTML extraction, as in the previous setup.

---

## 4. Steps Overview

### A. Scaffold a New electron-vite App
- [x] Created as `desktop-new-app` using React + TypeScript template.

### B. Initial Setup & Verification
- [x] App runs successfully in development mode.
- [x] Structure matches above reference.

### C. UI Framework Integration
- [x] Installed Tailwind CSS and daisyUI: `npm install tailwindcss @tailwindcss/vite daisyui`
- [x] Added `tailwind.config.js` with daisyUI plugin.
- [x] Updated `electron.vite.config.ts` to include Tailwind Vite plugin in renderer.
- [x] Imported Tailwind CSS in `src/renderer/src/assets/main.css`.
- [ ] Use daisyUI components in React code for beautiful, prebuilt UI.

### D. Security & Performance Hardening
- Ensure `nodeIntegration: false` and `contextIsolation: true` in all windows.
- Use preload scripts and `contextBridge` for safe API exposure.
- Enable sandboxing and strict Content Security Policy (CSP).
- Remove unnecessary polyfills and dependencies.
- Bundle code efficiently and avoid blocking operations in main/renderer.

### E. Migration of Main Components
- Identify and refactor main process logic, preload scripts, and renderer code from the old app.
- Move only validated, necessary code and dependencies.
- Migrate database logic, ensuring it runs in the main process or secure worker threads.
- Refactor IPC communication to validate senders and minimize surface area.

### F. Backend Integration
- Connect the new desktop app to the backend for authentication and HTML extraction.
- Reuse and adapt API calls as in the previous setup.

### G. Testing & Validation
- Test all migrated features in development mode.
- Profile performance and memory usage.
- Run security audits (e.g., Electronegativity).

### H. Production Build & Packaging
- Build the app for production (`npm run build`).
- Test the packaged app on all target platforms.

---

## 5. Key Best Practices
- **Security:**
  - Never enable Node.js integration in renderer.
  - Always use context isolation and sandboxing.
  - Validate all IPC messages and restrict exposed APIs.
  - Use HTTPS for all remote resources.
  - Define a strict CSP.
- **Performance:**
  - Profile and optimize before/after migration.
  - Defer heavy operations and avoid blocking the main thread.
  - Bundle and tree-shake code.
  - Remove unused dependencies and polyfills.
- **UI:**
  - Use Tailwind CSS utility classes and daisyUI prebuilt components for rapid, beautiful UI development.

---

## 6. Migration Checklist
- [x] Scaffold new electron-vite app
- [x] Verify dev environment
- [x] Integrate Tailwind CSS and daisyUI
- [ ] Harden security settings
- [ ] Migrate main process logic
- [ ] Migrate preload scripts
- [ ] Migrate renderer/frontend
- [ ] Migrate and test database logic
- [ ] Refactor IPC and exposed APIs
- [ ] Integrate backend API (auth, extraction)
- [ ] Test, profile, and audit
- [ ] Build and package for production

---

## 7. References
- [electron-vite Guide](https://electron-vite.org/guide/)
- [Electron Security Checklist](https://www.electronjs.org/docs/latest/tutorial/security)
- [Electron Performance Guide](https://www.electronjs.org/docs/latest/tutorial/performance)
- [Tailwind CSS + Vite](https://tailwindcss.com/docs/installation/using-vite)
