import React, { useState } from 'react'
import { useThemeContext } from '../context/useThemeContext'
import { Tabs } from '../components/ui/tabs'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { Card, CardContent } from '../components/ui/Card'
import { cn } from '../lib/aceternity-utils'

const Settings: React.FC = () => {
  const [autoSave, setAutoSave] = useState(true)
  const [language, setLanguage] = useState('en')
  const [complaintNumber, setComplaintNumber] = useState('')
  const [selectedMonth, setSelectedMonth] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteType, setDeleteType] = useState<'single' | 'bulk'>('single')
  const [templateFile, setTemplateFile] = useState<File | null>(null)
  const [hasTemplate, setHasTemplate] = useState(false)
  const [templateName, setTemplateName] = useState('')
  const [isUploadingTemplate, setIsUploadingTemplate] = useState(false)

  // Use theme context
  const { theme, setTheme } = useThemeContext()

  // Load existing template on component mount
  React.useEffect(() => {
    const loadTemplate = async (): Promise<void> => {
      try {
        const result = await window.api.template.get()
        if (result.success && result.template) {
          setHasTemplate(true)
          setTemplateName(result.template.name)
        }
      } catch (error) {
        console.error('Failed to load template:', error)
      }
    }
    loadTemplate()
  }, [])

  // Backup database function
  const handleBackupDatabase = async (): Promise<void> => {
    try {
      const result = await window.api.backupDatabase()
      if (result.success) {
        // Trigger download
        const link = document.createElement('a')
        link.href = `data:application/octet-stream;base64,${result.data}`
        link.download = `complaint_database_backup_${new Date().toISOString().split('T')[0]}.db`
        link.click()
      } else {
        console.error('Backup failed:', result.error)
      }
    } catch (error) {
      console.error('Backup error:', error)
    }
  }

  // Delete complaint functions
  const handleDeleteSingle = async (): Promise<void> => {
    if (!complaintNumber.trim()) return
    try {
      const result = await window.api.database.deleteComplaint(complaintNumber.trim())
      if (result) {
        setComplaintNumber('')
        setShowDeleteConfirm(false)
      }
    } catch (error) {
      console.error('Delete error:', error)
    }
  }

  const handleDeleteBulk = async (): Promise<void> => {
    if (!selectedMonth) return
    try {
      const result = await window.api.deleteComplaintsByMonth(selectedMonth)
      if (result.success) {
        setSelectedMonth('')
        setShowDeleteConfirm(false)
      }
    } catch (error) {
      console.error('Bulk delete error:', error)
    }
  }

  // Template management functions
  const handleTemplateUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ): Promise<void> => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploadingTemplate(true)
    try {
      const content = await file.text()
      const result = await window.api.template.store({
        name: file.name,
        content,
        fileName: file.name
      })

      if (result.success) {
        setHasTemplate(true)
        setTemplateName(file.name)
        setTemplateFile(file)
      } else {
        console.error('Template upload failed:', result.error)
      }
    } catch (error) {
      console.error('Template upload error:', error)
    } finally {
      setIsUploadingTemplate(false)
    }
  }

  const handleTemplateDelete = async (): Promise<void> => {
    try {
      const result = await window.api.template.delete()
      if (result.success) {
        setHasTemplate(false)
        setTemplateName('')
        setTemplateFile(null)
      } else {
        console.error('Template delete failed:', result.error)
      }
    } catch (error) {
      console.error('Template delete error:', error)
    }
  }

  const { isDark } = useThemeContext()

  const tabs = [
    {
      title: 'Application',
      value: 'application',
      content: (
        <div
          className={cn(
            'w-full overflow-hidden relative h-full rounded-2xl p-8 backdrop-blur-md border shadow-lg',
            isDark
              ? 'bg-white/10 border-white/10 text-white'
              : 'bg-white/80 border-gray-200/50 text-gray-900'
          )}
        >
          <h3 className={cn('text-2xl font-bold mb-8', isDark ? 'text-white' : 'text-gray-900')}>
            Application Settings
          </h3>
          <div className="space-y-6">
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <label
                  className={cn(
                    'text-lg font-semibold mb-2 block',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Theme
                </label>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Select your preferred color scheme.
                </p>
                <select
                  value={theme}
                  onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'system')}
                  className={cn(
                    'w-full p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500',
                    isDark
                      ? 'bg-white/10 border-white/20 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  )}
                >
                  <option value="system">System</option>
                  <option value="dark">Dark</option>
                  <option value="light">Light</option>
                </select>
              </CardContent>
            </Card>
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <label
                  className={cn(
                    'text-lg font-semibold mb-2 block',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Language
                </label>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Choose the application language.
                </p>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className={cn(
                    'w-full p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500',
                    isDark
                      ? 'bg-white/10 border-white/20 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  )}
                >
                  <option value="en">English</option>
                  <option value="hi">Hindi</option>
                  <option value="mr">Marathi</option>
                </select>
              </CardContent>
            </Card>

            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <label
                      className={cn(
                        'text-lg font-semibold',
                        isDark ? 'text-white' : 'text-gray-900'
                      )}
                    >
                      Auto Save
                    </label>
                    <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                      Automatically save changes as you make them.
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={autoSave}
                    onChange={(e) => setAutoSave(e.target.checked)}
                    className="w-6 h-6 rounded text-blue-500 focus:ring-blue-500"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      title: 'Data Management',
      value: 'data',
      content: (
        <div
          className={cn(
            'w-full overflow-hidden relative h-full rounded-2xl p-8 backdrop-blur-md border shadow-lg',
            isDark
              ? 'bg-white/10 border-white/10 text-white'
              : 'bg-white/80 border-gray-200/50 text-gray-900'
          )}
        >
          <h3 className={cn('text-2xl font-bold mb-8', isDark ? 'text-white' : 'text-gray-900')}>
            Data Management
          </h3>
          <div className="space-y-6">
            {/* Backup Section */}
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <h4
                  className={cn(
                    'text-lg font-semibold mb-2',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Database Backup
                </h4>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Download a backup copy of your complaint database.
                </p>
                <button
                  onClick={handleBackupDatabase}
                  className={cn(
                    'px-6 py-3 rounded-lg font-semibold transition-colors',
                    isDark
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  )}
                >
                  Download Backup
                </button>
              </CardContent>
            </Card>

            {/* Delete Single Complaint */}
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <h4
                  className={cn(
                    'text-lg font-semibold mb-2',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Delete Single Complaint
                </h4>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Delete a specific complaint by its complaint number.
                </p>
                <div className="flex gap-3">
                  <input
                    type="text"
                    placeholder="Enter complaint number"
                    value={complaintNumber}
                    onChange={(e) => setComplaintNumber(e.target.value)}
                    className={cn(
                      'flex-1 p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-red-500',
                      isDark
                        ? 'bg-white/10 border-white/20 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    )}
                  />
                  <button
                    onClick={() => {
                      setDeleteType('single')
                      setShowDeleteConfirm(true)
                    }}
                    disabled={!complaintNumber.trim()}
                    className="px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg font-semibold transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </CardContent>
            </Card>

            {/* Bulk Delete by Month */}
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <h4
                  className={cn(
                    'text-lg font-semibold mb-2',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Bulk Delete by Month
                </h4>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Delete all complaints from a specific month.
                </p>
                <div className="flex gap-3">
                  <input
                    type="month"
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                    className={cn(
                      'flex-1 p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-red-500',
                      isDark
                        ? 'bg-white/10 border-white/20 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    )}
                  />
                  <button
                    onClick={() => {
                      setDeleteType('bulk')
                      setShowDeleteConfirm(true)
                    }}
                    disabled={!selectedMonth}
                    className="px-6 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg font-semibold transition-colors"
                  >
                    Delete All
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      title: 'Templates',
      value: 'templates',
      content: (
        <div
          className={cn(
            'w-full overflow-hidden relative h-full rounded-2xl p-8 backdrop-blur-md border shadow-lg',
            isDark
              ? 'bg-white/10 border-white/10 text-white'
              : 'bg-white/80 border-gray-200/50 text-gray-900'
          )}
        >
          <h3 className={cn('text-2xl font-bold mb-8', isDark ? 'text-white' : 'text-gray-900')}>
            Template Management
          </h3>
          <div className="space-y-6">
            {/* Current Template Status */}
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <h4
                  className={cn(
                    'text-lg font-semibold mb-2',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Current Template
                </h4>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  {hasTemplate
                    ? `Template "${templateName}" is currently active.`
                    : 'No template uploaded. Upload a template to enable notice generation.'}
                </p>
                {hasTemplate && (
                  <button
                    onClick={handleTemplateDelete}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors"
                  >
                    Delete Template
                  </button>
                )}
              </CardContent>
            </Card>

            {/* Template Upload */}
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6">
                <h4
                  className={cn(
                    'text-lg font-semibold mb-2',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Upload New Template
                </h4>
                <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  Upload a Word document (.docx) template for notice generation. The new template
                  will replace any existing template.
                </p>
                <div className="flex items-center gap-3">
                  <input
                    type="file"
                    accept=".docx"
                    onChange={handleTemplateUpload}
                    disabled={isUploadingTemplate}
                    className={cn(
                      'flex-1 p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500',
                      isDark
                        ? 'bg-white/10 border-white/20 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    )}
                  />
                  {isUploadingTemplate && <div className="text-sm text-blue-500">Uploading...</div>}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    }
  ]

  // Confirmation Dialog
  const ConfirmationDialog = (): React.ReactElement | null => {
    if (!showDeleteConfirm) return null

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div
          className={cn(
            'p-6 rounded-lg backdrop-blur-md border shadow-lg max-w-md w-full mx-4',
            isDark
              ? 'bg-white/10 border-white/10 text-white'
              : 'bg-white/90 border-gray-200/50 text-gray-900'
          )}
        >
          <h3 className={cn('text-lg font-semibold mb-4', isDark ? 'text-white' : 'text-gray-900')}>
            Confirm Deletion
          </h3>
          <p className={cn('mb-6 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
            {deleteType === 'single'
              ? `Are you sure you want to delete complaint "${complaintNumber}"? This action cannot be undone.`
              : `Are you sure you want to delete all complaints from ${selectedMonth}? This action cannot be undone.`}
          </p>
          <div className="flex gap-3 justify-end">
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className={cn(
                'px-4 py-2 rounded-lg font-semibold transition-colors',
                isDark
                  ? 'bg-white/10 hover:bg-white/20 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
              )}
            >
              Cancel
            </button>
            <button
              onClick={deleteType === 'single' ? handleDeleteSingle : handleDeleteBulk}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <ConfirmationDialog />
      <FullWidthLayout
        enableLampBackground={false}
        enableGlassmorphism={true}
        maxWidth="none"
        padding="none"
        className="min-h-screen"
      >
        <UnifiedNavbar title="Settings" showBackButton />

        <main className="flex-1 p-6 w-full">
          <FullWidthSection
            title="Settings"
            subtitle="Manage your application preferences and configuration"
            className="mb-8"
          >
            <div className="p-6 max-w-5xl mx-auto h-full">
              <div className="h-[calc(100vh-12rem)] [perspective:1000px] relative flex flex-col w-full items-start justify-start">
                <Tabs tabs={tabs} />
              </div>
            </div>
          </FullWidthSection>
        </main>
      </FullWidthLayout>
    </>
  )
}

export default Settings
