import React, { useState, useMemo } from 'react'
import { Search, Mail, Phone, MapPin, Building } from 'lucide-react'
import {
  EnhancedModernTable,
  EnhancedColumnDef,
  CRUDOperations
} from '../components/ui/enhanced-modern-table'
import UnifiedNavbar from '../components/UnifiedNavbar'
import {
  FullWidthLayout,
  FullWidthSection
} from '../components/layout/FullWidthLayout'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

// Interface for nodal officer data
interface NodalOfficer {
  id: string
  name: string
  organization: string
  type: string
  designation: string
  email: string
  phone: string
  address: string
  state: string
}

// Dummy data for demonstration
const dummyNodalOfficers: NodalOfficer[] = [
  {
    id: '1',
    name: 'Amit <PERSON>',
    organization: 'State Bank of India',
    type: 'Bank',
    designation: 'Nodal Officer',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'Connaught Place, New Delhi',
    state: 'Delhi'
  },
  {
    id: '2',
    name: 'Priya Verma',
    organization: 'ICICI Bank',
    type: 'Bank',
    designation: 'Senior Manager',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'Bandra Kurla Complex, Mumbai',
    state: 'Maharashtra'
  },
  {
    id: '3',
    name: 'Rohit Singh',
    organization: 'Paytm Payments Bank',
    type: 'Wallet',
    designation: 'Nodal Officer',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'Sector 62, Noida',
    state: 'Uttar Pradesh'
  },
  {
    id: '4',
    name: 'Anjali Gupta',
    organization: 'HDFC Bank',
    type: 'Bank',
    designation: 'Assistant Manager',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'MG Road, Bangalore',
    state: 'Karnataka'
  },
  {
    id: '5',
    name: 'Vikram Patel',
    organization: 'PhonePe',
    type: 'Wallet',
    designation: 'Compliance Officer',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'Koramangala, Bangalore',
    state: 'Karnataka'
  },
  {
    id: '6',
    name: 'Sunita Rao',
    organization: 'Axis Bank',
    type: 'Bank',
    designation: 'Nodal Officer',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: 'Banjara Hills, Hyderabad',
    state: 'Telangana'
  }
]

const InformationSearchPage: React.FC = () => {
  const [search, setSearch] = useState('')
  const [results, setResults] = useState(dummyNodalOfficers)
  const { isDark } = useThemeContext()

  const handleSearch = (e: React.FormEvent): void => {
    e.preventDefault()
    const searchTerm = search.toLowerCase()
    setResults(
      dummyNodalOfficers.filter(
        (officer) =>
          officer.name.toLowerCase().includes(searchTerm) ||
          officer.organization.toLowerCase().includes(searchTerm) ||
          officer.state.toLowerCase().includes(searchTerm) ||
          officer.type.toLowerCase().includes(searchTerm) ||
          officer.designation.toLowerCase().includes(searchTerm) ||
          officer.email.toLowerCase().includes(searchTerm)
      )
    )
  }

  // Define table columns
  const columns: EnhancedColumnDef<NodalOfficer>[] = [
    {
      key: 'name',
      title: 'Name',
      type: 'text',
      width: 150,
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <div className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold',
            isDark ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-600'
          )}>
            {row.name.charAt(0).toUpperCase()}
          </div>
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'organization',
      title: 'Organization',
      type: 'text',
      width: 180,
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <Building className="w-4 h-4 text-gray-500" />
          <div>
            <div className="font-medium">{value}</div>
            <div className={cn(
              'text-xs',
              isDark ? 'text-gray-400' : 'text-gray-600'
            )}>{row.type}</div>
          </div>
        </div>
      )
    },
    {
      key: 'designation',
      title: 'Designation',
      type: 'text',
      width: 120
    },
    {
      key: 'email',
      title: 'Email',
      type: 'text',
      width: 200,
      render: (value) => (
        <div className="flex items-center gap-2">
          <Mail className="w-4 h-4 text-gray-500" />
          <a
            href={`mailto:${value}`}
            className={cn(
              'hover:underline',
              isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-700'
            )}
          >
            {value}
          </a>
        </div>
      )
    },
    {
      key: 'phone',
      title: 'Phone',
      type: 'text',
      width: 140,
      render: (value) => (
        <div className="flex items-center gap-2">
          <Phone className="w-4 h-4 text-gray-500" />
          <a
            href={`tel:${value}`}
            className={cn(
              'hover:underline',
              isDark ? 'text-green-400 hover:text-green-300' : 'text-green-600 hover:text-green-700'
            )}
          >
            {value}
          </a>
        </div>
      )
    },
    {
      key: 'address',
      title: 'Location',
      type: 'text',
      width: 200,
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-500" />
          <div>
            <div className="font-medium">{value}</div>
            <div className={cn(
              'text-xs',
              isDark ? 'text-gray-400' : 'text-gray-600'
            )}>{row.state}</div>
          </div>
        </div>
      )
    }
  ]

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title="Information Search"
        subtitle="Search for nodal officers and contact information"
        showBackButton
      />

      <main className="flex-1 p-6 w-full">
        <FullWidthSection
          title="Nodal Officer Directory"
          subtitle="Search and find contact information for bank and wallet nodal officers"
          className="mb-8"
        >
          <div className="p-6">
            {/* Search Section */}
            <div className={cn(
              'p-6 rounded-lg backdrop-blur-md border shadow-lg mb-6',
              isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
            )}>
              <form onSubmit={handleSearch} className="flex gap-4">
                <div className="flex-1 relative">
                  <Search className={cn(
                    'absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5',
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  )} />
                  <input
                    type="text"
                    placeholder="Search by name, organization, state, email..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className={cn(
                      'w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500',
                      isDark
                        ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    )}
                  />
                </div>
                <button
                  type="submit"
                  className={cn(
                    'px-6 py-3 rounded-lg font-semibold transition-colors',
                    'bg-blue-600 hover:bg-blue-700 text-white'
                  )}
                >
                  Search
                </button>
                {search && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearch('')
                      setResults(dummyNodalOfficers)
                    }}
                    className={cn(
                      'px-4 py-3 rounded-lg font-semibold transition-colors',
                      isDark
                        ? 'bg-white/10 hover:bg-white/20 text-white border border-white/20'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-900 border border-gray-300'
                    )}
                  >
                    Clear
                  </button>
                )}
              </form>
            </div>

            {/* Results Table */}
            <EnhancedModernTable
              data={results}
              columns={columns}
              loading={false}
              config={{
                enableSearch: false, // We have custom search above
                enablePagination: true,
                enableSorting: true,
                enableSelection: false,
                enableGlassmorphism: true,
                enableRowActions: false,
                pageSize: 10,
                searchPlaceholder: 'Search officers...'
              }}
              emptyMessage={search ? `No officers found matching "${search}"` : "No officers available"}
              className="w-full"
            />
          </div>
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default InformationSearchPage
