import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { ComplaintData } from '../../../shared/api'
import EnhancedComplaintTable from '../components/EnhancedComplaintTable'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const [complaintData, setComplaintData] = useState<ComplaintData[]>([])
  const [loading, setLoading] = useState(true)

  const fetchComplaints = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()
      setComplaintData(dbComplaints)
    } catch (error) {
      console.error('Failed to fetch complaints:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchComplaints()
  }, [])

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar title="Dashboard" subtitle="Manage and view all complaints" />

      <main className="flex-1 p-6 w-full">
        {/* Complaints Table Section */}
        <FullWidthSection title="Complaints" subtitle={`${complaintData.length} total complaints`}>
          <EnhancedComplaintTable
            complaints={complaintData}
            loading={loading}
            onRefresh={fetchComplaints}
          />
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default Dashboard
