import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { MapPin, Activity, Filter, Download, Settings, BarChart3 } from 'lucide-react'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { Button } from '../components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
import { backendService } from '../services/backendService'
import { ComplaintData } from '../../../shared/api'
import BankInfrastructureMap from '../components/maps/BankInfrastructureMap'
import FraudsterLocationMap from '../components/maps/FraudsterLocationMap'
import TimelineControl from '../components/analysis/TimelineControl'
import FilterPanel from '../components/analysis/FilterPanel'
import { exportService } from '../services/exportService'
import { hotspotAnalysisService } from '../services/hotspotAnalysisService'

// Import Leaflet CSS
import 'leaflet/dist/leaflet.css'

const AdvancedAnalysisPage: React.FC = () => {
  const navigate = useNavigate()
  const { isDark } = useThemeContext()
  const [complaints, setComplaints] = useState<ComplaintData[]>([])
  const [filteredData, setFilteredData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedComplaint, setSelectedComplaint] = useState<string | null>(null)
  const [analysisMode, setAnalysisMode] = useState<'bank-infrastructure' | 'fraudster-locations' | 'dual'>('dual')

  // Load all complaints for analysis
  useEffect(() => {
    const loadComplaints = async () => {
      try {
        setLoading(true)
        const complaintsData = await backendService.getComplaints()
        setComplaints(complaintsData)
      } catch (error) {
        console.error('Error loading complaints:', error)
      } finally {
        setLoading(false)
      }
    }

    loadComplaints()
  }, [])

  const handleComplaintSelect = (complaintId: string) => {
    setSelectedComplaint(complaintId)
  }

  const handleAnalysisModeChange = (mode: 'bank-infrastructure' | 'fraudster-locations' | 'dual') => {
    setAnalysisMode(mode)
  }

  const handleExport = async () => {
    try {
      // Generate hotspot analysis
      const hotspotResult = await hotspotAnalysisService.analyzeHotspots(
        filteredData?.complaints || complaints,
        { analysisType: 'combined' }
      )

      // Generate comprehensive report
      const report = exportService.generateAnalysisReport(
        filteredData?.complaints || complaints,
        filteredData,
        hotspotResult.hotspots,
        `${analysisMode} analysis`
      )

      // Generate evidence package
      await exportService.generateEvidencePackage(
        report,
        'advanced-analysis-maps', // Map container ID
        'fraud-investigation-evidence'
      )

      console.log('Export completed successfully')
    } catch (error) {
      console.error('Export failed:', error)
      // In a real app, show user-friendly error message
    }
  }

  if (loading) {
    return (
      <FullWidthLayout
        enableLampBackground={false}
        enableGlassmorphism={true}
        maxWidth="none"
        padding="none"
        className="min-h-screen"
      >
        <UnifiedNavbar title="Advanced Analysis" showBackButton />
        <main className="flex-1 p-6 w-full">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <div className="text-lg">Loading complaints data...</div>
            </div>
          </div>
        </main>
      </FullWidthLayout>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title="Advanced Analysis"
        subtitle="Dual Map Visualization for Fraud Investigation"
        showBackButton
        customActions={
          <div className="flex gap-2">
            <Button
              onClick={() => navigate('/all-stats')}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <BarChart3 className="w-4 h-4" />
              All Stats
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={handleExport}
            >
              <Download className="w-4 h-4" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>
          </div>
        }
      />

      <main className="flex-1 p-6 w-full">
        <div className="w-full space-y-6">
          {/* Advanced Filters */}
          <FullWidthSection
            title="Advanced Filters"
            subtitle="Filter and refine your analysis data"
            className="mb-6"
          >
            <FilterPanel
              complaints={complaints}
              onFilterChange={(criteria, filtered) => {
                console.log('Filter applied:', criteria, filtered)
                setFilteredData(filtered)
              }}
              className="w-full"
            />
          </FullWidthSection>

          {/* Control Panel */}
          <FullWidthSection
            title="Analysis Controls"
            subtitle="Configure your fraud investigation parameters"
            className="mb-6"
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Complaint Selection */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Filter className="w-5 h-5" />
                    Complaint Selection
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <select
                      value={selectedComplaint || ''}
                      onChange={(e) => handleComplaintSelect(e.target.value)}
                      className={cn(
                        'w-full p-2 rounded-lg border',
                        isDark
                          ? 'bg-white/10 border-white/20 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      )}
                    >
                      <option value="">All Complaints</option>
                      {complaints.map((complaint) => (
                        <option key={complaint.id} value={complaint.id}>
                          {complaint.title}
                        </option>
                      ))}
                    </select>
                    <div className="text-sm text-gray-500">
                      {complaints.length} complaints available
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Analysis Mode */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Analysis Mode
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { value: 'dual', label: 'Dual Map View', desc: 'Bank infrastructure + Fraudster locations' },
                      { value: 'bank-infrastructure', label: 'Bank Infrastructure', desc: 'IFSC-based bank branch analysis' },
                      { value: 'fraudster-locations', label: 'Fraudster Locations', desc: 'Address-based location intelligence' }
                    ].map((mode) => (
                      <label key={mode.value} className="flex items-start gap-3 cursor-pointer">
                        <input
                          type="radio"
                          name="analysisMode"
                          value={mode.value}
                          checked={analysisMode === mode.value}
                          onChange={(e) => handleAnalysisModeChange(e.target.value as any)}
                          className="mt-1"
                        />
                        <div>
                          <div className="font-medium">{mode.label}</div>
                          <div className="text-sm text-gray-500">{mode.desc}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Quick Stats
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Total Complaints:</span>
                      <span className="font-semibold">{complaints.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Selected:</span>
                      <span className="font-semibold">
                        {selectedComplaint ? '1' : complaints.length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Analysis Mode:</span>
                      <span className="font-semibold capitalize">
                        {analysisMode.replace('-', ' ')}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </FullWidthSection>

          {/* Map Visualization Area */}
          <FullWidthSection
            title="Fraud Investigation Maps"
            subtitle="Interactive geospatial analysis of fraud patterns"
            className="mb-6"
          >
            <div id="advanced-analysis-maps">
            {analysisMode === 'dual' ? (
              <div className="space-y-8">
                {/* Bank Infrastructure Map */}
                <div>
                  <div className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <MapPin className="w-5 h-5 text-blue-500" />
                    Bank Infrastructure Analysis
                  </div>
                  <BankInfrastructureMap
                    complaints={filteredData?.complaints || complaints}
                    selectedComplaint={selectedComplaint}
                    className="w-full"
                  />
                </div>

                {/* Fraudster Location Map */}
                <div>
                  <div className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Activity className="w-5 h-5 text-red-500" />
                    Fraudster Location Intelligence
                  </div>
                  <FraudsterLocationMap
                    complaints={filteredData?.complaints || complaints}
                    selectedComplaint={selectedComplaint}
                    className="w-full"
                  />
                </div>
              </div>
            ) : analysisMode === 'bank-infrastructure' ? (
              <div className="space-y-6">
                <BankInfrastructureMap
                  complaints={filteredData?.complaints || complaints}
                  selectedComplaint={selectedComplaint}
                  className="w-full"
                />
              </div>
            ) : (
              <div className="space-y-6">
                <FraudsterLocationMap
                  complaints={filteredData?.complaints || complaints}
                  selectedComplaint={selectedComplaint}
                  className="w-full"
                />
              </div>
            )}
            </div>
          </FullWidthSection>

          {/* Temporal Analysis Controls */}
          <FullWidthSection
            title="Temporal Analysis"
            subtitle="Timeline controls and fraud progression visualization"
            className="mb-6"
          >
            <TimelineControl
              complaints={filteredData?.complaints || complaints}
              selectedComplaint={selectedComplaint}
              onTimeRangeChange={(startDate, endDate) => {
                console.log('Time range changed:', startDate, endDate)
                // Handle time range changes for filtering map data
              }}
              onAnimationFrame={(currentDate, events) => {
                console.log('Animation frame:', currentDate, events.length)
                // Handle animation frames for real-time visualization
              }}
              className="w-full"
            />
          </FullWidthSection>
        </div>
      </main>
    </FullWidthLayout>
  )
}

export default AdvancedAnalysisPage
