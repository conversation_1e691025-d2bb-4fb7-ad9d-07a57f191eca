import React, { useState, useContext, useEffect } from 'react'
import { AuthContext } from '../context/AuthContext'
import UnifiedNavbar from '../components/UnifiedNavbar'
import {
  FullWidthLayout,
  FullWidthSection,
  ResponsiveGrid
} from '../components/layout/FullWidthLayout'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

const Profile: React.FC = () => {
  const authContext = useContext(AuthContext)
  const user = authContext?.user
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    initial: user?.initial || '',
    name: user?.name || '',
    email: user?.email || '',
    designation: user?.designation || '',
    police_station: user?.police_station || '',
    district: user?.district || '',
    state: user?.state || ''
  })

  useEffect(() => {
    if (user) {
      setFormData({
        initial: user.initial || '',
        name: user.name || '',
        email: user.email || '',
        designation: user.designation || '',
        police_station: user.police_station || '',
        district: user.district || '',
        state: user.state || ''
      })
    }
  }, [user])
  const [loading, setLoading] = useState(false)

  const subscriptionData = {
    plan: 'Professional',
    status: 'active',
    expiryDate: '2025-12-31',
    features: ['Unlimited Complaints', 'Advanced Analytics', 'Priority Support', 'Custom Templates']
  }

  const handleInputChange = (field: string, value: string): void => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSave = async (): Promise<void> => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      setIsEditing(false)
    }, 1000)
  }

  const handleCancel = (): void => {
    setFormData({
      initial: user?.initial || '',
      name: user?.name || '',
      email: user?.email || '',
      designation: user?.designation || '',
      police_station: user?.police_station || '',
      district: user?.district || '',
      state: user?.state || ''
    })
    setIsEditing(false)
  }

  const renderInfoField = (label: string, value: string): React.ReactNode => (
    <div>
      <label className={cn('text-sm font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}>
        {label}
      </label>
      <div
        className={cn(
          'mt-1 p-3 rounded-lg',
          isDark ? 'bg-white/10 text-white' : 'bg-gray-100 text-gray-900'
        )}
      >
        {value || 'Not specified'}
      </div>
    </div>
  )

  const renderInputField = (
    label: string,
    field: keyof typeof formData,
    value: string
  ): React.ReactNode => (
    <div>
      <label
        htmlFor={field}
        className={cn('text-sm font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}
      >
        {label}
      </label>
      <input
        id={field}
        value={value}
        onChange={(e) => handleInputChange(field, e.target.value)}
        className={cn(
          'w-full mt-1 p-3 border-b-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
          isDark
            ? 'bg-transparent border-white/20 text-white'
            : 'bg-white border-gray-300 text-gray-900'
        )}
      />
    </div>
  )

  const { isDark } = useThemeContext()

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar title="Profile" showBackButton />

      <main className="flex-1 p-6 w-full">
        <FullWidthSection
          title="Profile"
          subtitle="Manage your personal information and subscription details"
          className="mb-8"
        >
          <ResponsiveGrid columns={{ sm: 1, lg: 3 }} className="gap-8">
            <Card
              className={cn(
                'lg:col-span-2 backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
              )}
            >
              <CardHeader>
                <CardTitle
                  className={cn('text-2xl font-bold', isDark ? 'text-white' : 'text-gray-900')}
                >
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isEditing ? (
                    <>
                      <div>
                        <label
                          htmlFor="initial"
                          className={cn(
                            'text-sm font-semibold',
                            isDark ? 'text-gray-400' : 'text-gray-600'
                          )}
                        >
                          Initial
                        </label>
                        <select
                          id="initial"
                          value={formData.initial}
                          onChange={(e) => handleInputChange('initial', e.target.value)}
                          className={cn(
                            'w-full mt-1 p-3 border-b-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                            isDark
                              ? 'bg-transparent border-white/20 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          )}
                        >
                          <option value="">Select Initial</option>
                          <option value="Mr.">Mr.</option>
                          <option value="Miss">Miss</option>
                          <option value="Mrs.">Mrs.</option>
                        </select>
                      </div>
                      {renderInputField('Name', 'name', formData.name)}
                    </>
                  ) : (
                    <>
                      {renderInfoField('Initial', formData.initial)}
                      {renderInfoField('Name', formData.name)}
                    </>
                  )}
                </div>
                <div>
                  <label
                    className={cn(
                      'text-sm font-semibold',
                      isDark ? 'text-gray-400' : 'text-gray-600'
                    )}
                  >
                    Email Address
                  </label>
                  <div
                    className={cn(
                      'mt-1 p-3 rounded-lg cursor-not-allowed',
                      isDark ? 'bg-white/5 text-gray-400' : 'bg-gray-50 text-gray-500'
                    )}
                  >
                    {formData.email} (Cannot be changed)
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isEditing
                    ? renderInputField('Designation', 'designation', formData.designation)
                    : renderInfoField('Designation', formData.designation)}
                  {isEditing
                    ? renderInputField('Police Station', 'police_station', formData.police_station)
                    : renderInfoField('Police Station', formData.police_station)}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isEditing
                    ? renderInputField('District', 'district', formData.district)
                    : renderInfoField('District', formData.district)}
                  {isEditing
                    ? renderInputField('State', 'state', formData.state)
                    : renderInfoField('State', formData.state)}
                </div>

                <div className="flex gap-4">
                  {!isEditing ? (
                    <Button onClick={() => setIsEditing(true)} className="w-full">
                      Edit Profile
                    </Button>
                  ) : (
                    <>
                      <Button onClick={handleCancel} variant="outline" className="w-full">
                        Cancel
                      </Button>
                      <Button onClick={handleSave} disabled={loading} className="w-full">
                        {loading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
              )}
            >
              <CardHeader>
                <CardTitle
                  className={cn('text-2xl font-bold', isDark ? 'text-white' : 'text-gray-900')}
                >
                  Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <span className={cn('font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}>
                    Plan:
                  </span>{' '}
                  <span className={cn(isDark ? 'text-white' : 'text-gray-900')}>
                    {subscriptionData.plan}
                  </span>
                </div>

                <div>
                  <span className={cn('font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}>
                    Status:
                  </span>{' '}
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      subscriptionData.status === 'active'
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                    }`}
                  >
                    {subscriptionData.status}
                  </span>
                </div>

                <div>
                  <span className={cn('font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}>
                    Expires:
                  </span>{' '}
                  <span className={cn(isDark ? 'text-white' : 'text-gray-900')}>
                    {new Date(subscriptionData.expiryDate).toLocaleDateString('en-IN')}
                  </span>
                </div>

                <div>
                  <span className={cn('font-semibold', isDark ? 'text-gray-400' : 'text-gray-600')}>
                    Features:
                  </span>
                  <ul
                    className={cn(
                      'list-disc list-inside mt-2 space-y-1',
                      isDark ? 'text-white' : 'text-gray-900'
                    )}
                  >
                    {subscriptionData.features.map((f, i) => (
                      <li key={i}>{f}</li>
                    ))}
                  </ul>
                </div>

                <Button className="w-full mt-4">Manage Subscription</Button>
              </CardContent>
            </Card>
          </ResponsiveGrid>
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default Profile
