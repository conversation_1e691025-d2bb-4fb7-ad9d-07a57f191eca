import React from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useComplaintData } from '../hooks/useComplaintData'
import { TransactionData } from '../../../shared/api'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { Button } from '../components/ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
// Individual transaction analysis only

const StatsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark } = useThemeContext()
  const { data: complaint, loading, error } = useComplaintData(id || '')

  // Individual transaction analysis only - no tabs needed

  // Extract all transactions from layer_transactions
  const allTransactions: TransactionData[] = React.useMemo(() => {
    if (!complaint?.layer_transactions) return []

    return Object.values(complaint.layer_transactions)
      .flat()
      .filter(
        (item): item is TransactionData =>
          typeof item === 'object' && item !== null && 'receiver_ifsc' in item
      )
  }, [complaint])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg">Loading complaint data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Error</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600">Complaint not found</div>
        </div>
      </div>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title={complaint ? `Statistics - ${complaint.title}` : 'Statistics'}
        showBackButton
        customActions={
          <Button onClick={() => navigate('/all-stats')} variant="outline" size="sm">
            All Stats
          </Button>
        }
      />

      <main className="flex-1 p-6 w-full">
        <div className="w-full">
          <div className="space-y-6">
            <FullWidthSection
              title={`Statistics for ${complaint.title}`}
              subtitle="Detailed analysis of transactions and patterns"
              className="mb-8"
            >
              <div
                className={cn(
                  'p-8 rounded-lg backdrop-blur-md border shadow-lg text-center',
                  isDark
                    ? 'bg-white/10 border-white/10 text-white'
                    : 'bg-white/80 border-gray-200/50 text-gray-900'
                )}
              >
                <p className="text-lg">
                  Transaction analysis for complaint {id} with {allTransactions.length} transactions
                </p>
              </div>
            </FullWidthSection>
          </div>
        </div>
      </main>
    </FullWidthLayout>
  )
}

export default StatsPage
