import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { ComplaintData, GraphNode, GraphEdge } from '../../../../shared/api'
import { useThemeContext } from '../../context/useThemeContext'

// Unified app state interface
interface UnifiedAppState {
  // Data state
  complaints: ComplaintData[]
  currentComplaint: ComplaintData | null
  loading: boolean
  error: string | null

  // UI state
  sidebarCollapsed: boolean
  activeView: 'dashboard' | 'complaints' | 'graph' | 'notices' | 'settings'
  tableView: 'enhanced' | 'legacy'

  // Feature flags
  features: {
    enhancedTables: boolean
    glassmorphism: boolean
    fullWidthLayout: boolean
    editableNodes: boolean
    auditLogging: boolean
  }
}

// Unified app actions interface
interface UnifiedAppActions {
  // Data actions
  loadComplaints: () => Promise<void>
  loadComplaint: (id: string) => Promise<void>
  createComplaint: (
    data: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>
  ) => Promise<string>
  updateComplaint: (id: string, data: Partial<ComplaintData>) => Promise<void>
  deleteComplaint: (id: string) => Promise<void>

  // Graph actions
  updateGraphNode: (complaintId: string, nodeId: string, data: Partial<GraphNode>) => Promise<void>
  addGraphNode: (complaintId: string, node: GraphNode) => Promise<void>
  deleteGraphNode: (complaintId: string, nodeId: string) => Promise<void>
  addGraphEdge: (complaintId: string, edge: GraphEdge) => Promise<void>

  // UI actions
  setSidebarCollapsed: (collapsed: boolean) => void
  setActiveView: (view: UnifiedAppState['activeView']) => void
  setTableView: (view: UnifiedAppState['tableView']) => void
  setError: (error: string | null) => void

  // Feature toggles
  toggleFeature: (feature: keyof UnifiedAppState['features']) => void
}

// Combined context interface
interface UnifiedAppContextType extends UnifiedAppState, UnifiedAppActions {}

// Create context
const UnifiedAppContext = createContext<UnifiedAppContextType | null>(null)

// Default state
const defaultState: UnifiedAppState = {
  complaints: [],
  currentComplaint: null,
  loading: false,
  error: null,
  sidebarCollapsed: false,
  activeView: 'dashboard',
  tableView: 'enhanced',
  features: {
    enhancedTables: true,
    glassmorphism: true,
    fullWidthLayout: true,
    editableNodes: true,
    auditLogging: true
  }
}

// Provider component
interface UnifiedAppProviderProps {
  children: ReactNode
}

export const UnifiedAppProvider: React.FC<UnifiedAppProviderProps> = ({ children }) => {
  const [state, setState] = useState<UnifiedAppState>(defaultState)
  const { isDark } = useThemeContext()

  // Load initial data
  useEffect(() => {
    loadComplaints()
  }, [])

  // Persist feature flags to localStorage
  useEffect(() => {
    localStorage.setItem('unifiedApp.features', JSON.stringify(state.features))
  }, [state.features])

  // Load feature flags from localStorage
  useEffect(() => {
    const savedFeatures = localStorage.getItem('unifiedApp.features')
    if (savedFeatures) {
      try {
        const features = JSON.parse(savedFeatures)
        setState((prev) => ({ ...prev, features: { ...prev.features, ...features } }))
      } catch (error) {
        console.error('Failed to load saved features:', error)
      }
    }
  }, [])

  // Data actions implementation
  const loadComplaints = async (): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const complaints = await window.api.database.getComplaints()
      setState((prev) => ({ ...prev, complaints, loading: false }))
    } catch (error) {
      console.error('Failed to load complaints:', error)
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to load complaints: ${error}`
      }))
    }
  }

  const loadComplaint = async (id: string): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const complaint = await window.api.database.getComplaint(id)
      setState((prev) => ({ ...prev, currentComplaint: complaint, loading: false }))
    } catch (error) {
      console.error('Failed to load complaint:', error)
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to load complaint: ${error}`
      }))
    }
  }

  const createComplaint = async (
    data: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>
  ): Promise<string> => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const id = await window.api.database.storeComplaint(data)
      await loadComplaints() // Refresh the list
      setState((prev) => ({ ...prev, loading: false }))
      return id
    } catch (error) {
      console.error('Failed to create complaint:', error)
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to create complaint: ${error}`
      }))
      throw error
    }
  }

  const updateComplaint = async (id: string, data: Partial<ComplaintData>): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      await window.api.database.updateComplaint(id, data)
      await loadComplaints() // Refresh the list

      // Update current complaint if it's the one being updated
      if (state.currentComplaint?.id === id) {
        await loadComplaint(id)
      }

      setState((prev) => ({ ...prev, loading: false }))
    } catch (error) {
      console.error('Failed to update complaint:', error)
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to update complaint: ${error}`
      }))
      throw error
    }
  }

  const deleteComplaint = async (id: string): Promise<void> => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      await window.api.database.deleteComplaint(id)
      await loadComplaints() // Refresh the list

      // Clear current complaint if it's the one being deleted
      if (state.currentComplaint?.id === id) {
        setState((prev) => ({ ...prev, currentComplaint: null }))
      }

      setState((prev) => ({ ...prev, loading: false }))
    } catch (error) {
      console.error('Failed to delete complaint:', error)
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to delete complaint: ${error}`
      }))
      throw error
    }
  }

  // Graph actions implementation
  const updateGraphNode = async (
    complaintId: string,
    nodeId: string,
    data: Partial<GraphNode>
  ): Promise<void> => {
    try {
      // Update the complaint data directly since the specific API methods don't exist yet
      const complaint = await window.api.database.getComplaint(complaintId)
      if (complaint && complaint.graph_data?.nodes) {
        const nodeIndex = complaint.graph_data.nodes.findIndex((n) => n.id === nodeId)
        if (nodeIndex !== -1) {
          complaint.graph_data.nodes[nodeIndex] = {
            ...complaint.graph_data.nodes[nodeIndex],
            ...data
          }
          await window.api.database.updateComplaint(complaintId, {
            graph_data: complaint.graph_data
          })
        }
      }

      // Refresh current complaint if it matches
      if (state.currentComplaint?.id === complaintId) {
        await loadComplaint(complaintId)
      }
    } catch (error) {
      console.error('Failed to update graph node:', error)
      setState((prev) => ({
        ...prev,
        error: `Failed to update graph node: ${error}`
      }))
      throw error
    }
  }

  const addGraphNode = async (complaintId: string, node: GraphNode): Promise<void> => {
    try {
      // Update the complaint data directly since the specific API methods don't exist yet
      const complaint = await window.api.database.getComplaint(complaintId)
      if (complaint) {
        if (!complaint.graph_data) {
          complaint.graph_data = {
            nodes: [],
            edges: [],
            transactions: [],
            metadata: {},
            max_layer: 0
          }
        }
        if (!complaint.graph_data.nodes) {
          complaint.graph_data.nodes = []
        }
        complaint.graph_data.nodes.push(node)
        await window.api.database.updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }

      // Refresh current complaint if it matches
      if (state.currentComplaint?.id === complaintId) {
        await loadComplaint(complaintId)
      }
    } catch (error) {
      console.error('Failed to add graph node:', error)
      setState((prev) => ({
        ...prev,
        error: `Failed to add graph node: ${error}`
      }))
      throw error
    }
  }

  const deleteGraphNode = async (complaintId: string, nodeId: string): Promise<void> => {
    try {
      // Update the complaint data directly since the specific API methods don't exist yet
      const complaint = await window.api.database.getComplaint(complaintId)
      if (complaint && complaint.graph_data?.nodes) {
        complaint.graph_data.nodes = complaint.graph_data.nodes.filter((n) => n.id !== nodeId)
        await window.api.database.updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }

      // Refresh current complaint if it matches
      if (state.currentComplaint?.id === complaintId) {
        await loadComplaint(complaintId)
      }
    } catch (error) {
      console.error('Failed to delete graph node:', error)
      setState((prev) => ({
        ...prev,
        error: `Failed to delete graph node: ${error}`
      }))
      throw error
    }
  }

  // Add graph edge functionality using GraphEdge type
  const addGraphEdge = async (complaintId: string, edge: GraphEdge): Promise<void> => {
    try {
      const complaint = await window.api.database.getComplaint(complaintId)
      if (complaint) {
        if (!complaint.graph_data) {
          complaint.graph_data = {
            nodes: [],
            edges: [],
            transactions: [],
            metadata: {},
            max_layer: 0
          }
        }
        if (!complaint.graph_data.edges) {
          complaint.graph_data.edges = []
        }
        complaint.graph_data.edges.push(edge)
        await window.api.database.updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }

      // Refresh current complaint if it matches
      if (state.currentComplaint?.id === complaintId) {
        await loadComplaint(complaintId)
      }
    } catch (error) {
      console.error('Failed to add graph edge:', error)
      setState((prev) => ({
        ...prev,
        error: `Failed to add graph edge: ${error}`
      }))
      throw error
    }
  }

  // UI actions implementation
  const setSidebarCollapsed = (collapsed: boolean): void => {
    setState((prev) => ({ ...prev, sidebarCollapsed: collapsed }))
  }

  const setActiveView = (view: UnifiedAppState['activeView']): void => {
    setState((prev) => ({ ...prev, activeView: view }))
  }

  const setTableView = (view: UnifiedAppState['tableView']): void => {
    setState((prev) => ({ ...prev, tableView: view }))
  }

  const setError = (error: string | null): void => {
    setState((prev) => ({ ...prev, error }))
  }

  const toggleFeature = (feature: keyof UnifiedAppState['features']): void => {
    setState((prev) => ({
      ...prev,
      features: {
        ...prev.features,
        [feature]: !prev.features[feature]
      }
    }))
  }

  // Context value
  const contextValue: UnifiedAppContextType = {
    ...state,
    loadComplaints,
    loadComplaint,
    createComplaint,
    updateComplaint,
    deleteComplaint,
    updateGraphNode,
    addGraphNode,
    deleteGraphNode,
    addGraphEdge,
    setSidebarCollapsed,
    setActiveView,
    setTableView,
    setError,
    toggleFeature
  }

  return (
    <UnifiedAppContext.Provider value={contextValue}>
      <div className={isDark ? 'dark' : 'light'}>
        <UnifiedAppErrorBoundary>{children}</UnifiedAppErrorBoundary>
      </div>
    </UnifiedAppContext.Provider>
  )
}

// Hook to use the unified app context
export const useUnifiedApp = (): UnifiedAppContextType => {
  const context = useContext(UnifiedAppContext)
  if (!context) {
    throw new Error('useUnifiedApp must be used within a UnifiedAppProvider')
  }
  return context
}

// Error boundary for the unified app
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

export class UnifiedAppErrorBoundary extends React.Component<
  { children: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode }) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error('Unified App Error:', error, errorInfo)
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center p-8">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
            <p className="text-gray-600 mb-4">
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Application
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Integration testing utilities
export const testUnifiedAppIntegration = async (): Promise<{
  success: boolean
  results: Array<{ test: string; passed: boolean; error?: string }>
}> => {
  const results: Array<{ test: string; passed: boolean; error?: string }> = []

  // Test 1: Database connectivity
  try {
    await window.api.database.getComplaints()
    results.push({ test: 'Database connectivity', passed: true })
  } catch (error) {
    results.push({
      test: 'Database connectivity',
      passed: false,
      error: String(error)
    })
  }

  // Test 2: Local storage persistence
  try {
    const testData = { test: true }
    localStorage.setItem('test-integration', JSON.stringify(testData))
    const retrieved = JSON.parse(localStorage.getItem('test-integration') || '{}')
    localStorage.removeItem('test-integration')

    if (retrieved.test === true) {
      results.push({ test: 'Local storage persistence', passed: true })
    } else {
      results.push({
        test: 'Local storage persistence',
        passed: false,
        error: 'Data mismatch'
      })
    }
  } catch (error) {
    results.push({
      test: 'Local storage persistence',
      passed: false,
      error: String(error)
    })
  }

  // Test 3: Theme context availability
  try {
    const themeTest =
      document.documentElement.classList.contains('dark') ||
      document.documentElement.classList.contains('light')
    results.push({
      test: 'Theme context availability',
      passed: themeTest
    })
  } catch (error) {
    results.push({
      test: 'Theme context availability',
      passed: false,
      error: String(error)
    })
  }

  // Test 4: Component rendering capability
  try {
    const testDiv = document.createElement('div')
    testDiv.className = 'backdrop-blur-md bg-white/70'
    document.body.appendChild(testDiv)
    const styles = window.getComputedStyle(testDiv)
    document.body.removeChild(testDiv)

    results.push({
      test: 'Component rendering capability',
      passed:
        styles.backdropFilter !== undefined || (styles as any).webkitBackdropFilter !== undefined
    })
  } catch (error) {
    results.push({
      test: 'Component rendering capability',
      passed: false,
      error: String(error)
    })
  }

  const success = results.every((result) => result.passed)
  return { success, results }
}

// Performance monitoring utilities
export const monitorAppPerformance = (): {
  renderTime: number
  memoryUsage: number
  dbQueryTime: number
} => {
  const metrics = {
    renderTime: 0,
    memoryUsage: 0,
    dbQueryTime: 0
  }

  // Monitor render performance
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'measure') {
        metrics.renderTime = entry.duration
      }
    }
  })
  observer.observe({ entryTypes: ['measure'] })

  // Monitor memory usage (if available)
  if ('memory' in performance) {
    const memInfo = (performance as any).memory
    metrics.memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024 // MB
  }

  return metrics
}

export default UnifiedAppProvider
