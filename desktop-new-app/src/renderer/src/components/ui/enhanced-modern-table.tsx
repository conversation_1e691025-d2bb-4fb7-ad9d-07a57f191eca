import React, { useState, useCallback, useMemo, useEffect } from 'react'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  PaginationState,
  ColumnDef,
  FilterFn
} from '@tanstack/react-table'
import { motion, AnimatePresence } from 'motion/react'
import { 
  FiEdit2, 
  FiTrash2, 
  FiCheck, 
  FiX, 
  FiPlus, 
  FiSearch,
  FiChevronLeft,
  FiChevronRight,
  FiMoreVertical,
  FiSave
} from 'react-icons/fi'
import { cn } from '../../lib/aceternity-utils'
import { useThemeContext } from '../../context/useThemeContext'
import { GlowingEffect } from './glowing-effect'

// Generic row interface
export interface TableRow {
  id: string
  [key: string]: any
}

// CRUD operation types
export interface CRUDOperations<T extends TableRow> {
  onCreate?: (data: Omit<T, 'id'>) => Promise<T> | T
  onUpdate?: (id: string, data: Partial<T>) => Promise<T> | T
  onDelete?: (id: string) => Promise<void> | void
  onBulkDelete?: (ids: string[]) => Promise<void> | void
}

// Table configuration
export interface TableConfig {
  enableSearch?: boolean
  enablePagination?: boolean
  enableSorting?: boolean
  enableSelection?: boolean
  enableGlassmorphism?: boolean
  enableRowActions?: boolean
  pageSize?: number
  searchPlaceholder?: string
}

// Column configuration with enhanced features
export interface EnhancedColumnDef<T extends TableRow> extends Omit<ColumnDef<T>, 'cell'> {
  key: keyof T
  title: string
  type?: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'custom'
  editable?: boolean
  required?: boolean
  options?: { value: any; label: string }[]
  render?: (value: any, row: T) => React.ReactNode
  cell?: (props: any) => React.ReactNode
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
}

interface EnhancedModernTableProps<T extends TableRow> {
  data: T[]
  columns: EnhancedColumnDef<T>[]
  operations?: CRUDOperations<T>
  config?: TableConfig
  loading?: boolean
  className?: string
  onRowClick?: (row: T) => void
  emptyMessage?: string
}

const defaultConfig: TableConfig = {
  enableSearch: true,
  enablePagination: true,
  enableSorting: true,
  enableSelection: false,
  enableGlassmorphism: true,
  enableRowActions: true,
  pageSize: 10,
  searchPlaceholder: 'Search...'
}

// Global filter function
const globalFilterFn: FilterFn<any> = (row, columnId, value) => {
  const search = value.toLowerCase()
  return Object.values(row.original).some((val) =>
    String(val).toLowerCase().includes(search)
  )
}

export function EnhancedModernTable<T extends TableRow>({
  data,
  columns,
  operations,
  config = {},
  loading = false,
  className,
  onRowClick,
  emptyMessage = 'No data available'
}: EnhancedModernTableProps<T>) {
  const { isDark } = useThemeContext()
  const finalConfig = { ...defaultConfig, ...config }
  
  // State management
  const [sorting, setSorting] = useState<SortingState>([])
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: finalConfig.pageSize!
  })
  const [globalFilter, setGlobalFilter] = useState('')
  const [editingRow, setEditingRow] = useState<string | null>(null)
  const [editedData, setEditedData] = useState<Partial<T>>({})
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [isCreating, setIsCreating] = useState(false)
  const [newRowData, setNewRowData] = useState<Partial<T>>({})
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null)

  // Column helper
  const columnHelper = createColumnHelper<T>()

  // Build table columns
  const tableColumns = useMemo(() => {
    const cols: ColumnDef<T>[] = []

    // Selection column
    if (finalConfig.enableSelection) {
      cols.push(
        columnHelper.display({
          id: 'select',
          header: ({ table }) => (
            <input
              type="checkbox"
              checked={table.getIsAllRowsSelected()}
              onChange={table.getToggleAllRowsSelectedHandler()}
              className="rounded border-gray-300 dark:border-gray-600"
            />
          ),
          cell: ({ row }) => (
            <input
              type="checkbox"
              checked={selectedRows.has(row.original.id)}
              onChange={() => {
                const newSelected = new Set(selectedRows)
                if (newSelected.has(row.original.id)) {
                  newSelected.delete(row.original.id)
                } else {
                  newSelected.add(row.original.id)
                }
                setSelectedRows(newSelected)
              }}
              className="rounded border-gray-300 dark:border-gray-600"
            />
          ),
          size: 50
        })
      )
    }

    // Data columns
    columns.forEach((col) => {
      cols.push(
        columnHelper.accessor(col.key as any, {
          id: String(col.key),
          header: col.title,
          enableSorting: finalConfig.enableSorting && col.enableSorting !== false,
          cell: ({ row, getValue }) => {
            const value = getValue()
            const isEditing = editingRow === row.original.id

            if (isEditing && col.editable) {
              return renderEditCell(col, value, (newValue) => {
                setEditedData(prev => ({ ...prev, [col.key]: newValue }))
              })
            }

            if (col.render) {
              return col.render(value, row.original)
            }

            if (col.cell) {
              return col.cell({ row, getValue })
            }

            return renderDisplayCell(col, value)
          },
          size: col.width as number,
          minSize: col.minWidth as number,
          maxSize: col.maxWidth as number
        })
      )
    })

    // Actions column
    if (finalConfig.enableRowActions && (operations?.onUpdate || operations?.onDelete)) {
      cols.push(
        columnHelper.display({
          id: 'actions',
          header: 'Actions',
          cell: ({ row }) => renderActionCell(row.original),
          size: 120
        })
      )
    }

    return cols
  }, [columns, finalConfig, editingRow, selectedRows, operations])

  // Render edit cell based on column type
  const renderEditCell = (col: EnhancedColumnDef<T>, value: any, onChange: (value: any) => void) => {
    const baseClasses = cn(
      'w-full px-2 py-1 text-sm rounded border',
      'focus:outline-none focus:ring-2 focus:ring-blue-500',
      isDark 
        ? 'bg-gray-800 border-gray-600 text-white' 
        : 'bg-white border-gray-300 text-gray-900'
    )

    switch (col.type) {
      case 'select':
        return (
          <select
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
          >
            <option value="">Select...</option>
            {col.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )
      
      case 'boolean':
        return (
          <input
            type="checkbox"
            checked={Boolean(value)}
            onChange={(e) => onChange(e.target.checked)}
            className="rounded border-gray-300 dark:border-gray-600"
          />
        )
      
      case 'number':
        return (
          <input
            type="number"
            value={value || ''}
            onChange={(e) => onChange(Number(e.target.value))}
            className={baseClasses}
          />
        )
      
      case 'date':
        return (
          <input
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
          />
        )
      
      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
          />
        )
    }
  }

  // Render display cell
  const renderDisplayCell = (col: EnhancedColumnDef<T>, value: any) => {
    if (value === null || value === undefined) return '-'
    
    switch (col.type) {
      case 'boolean':
        return (
          <span className={cn(
            'px-2 py-1 rounded-full text-xs font-medium',
            value 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          )}>
            {value ? 'Yes' : 'No'}
          </span>
        )
      
      case 'date':
        return new Date(value).toLocaleDateString()
      
      case 'number':
        return typeof value === 'number' ? value.toLocaleString() : value
      
      default:
        return String(value)
    }
  }

  // Render action cell
  const renderActionCell = (row: T) => {
    const isEditing = editingRow === row.id
    
    if (isEditing) {
      return (
        <div className="flex items-center gap-1">
          <button
            onClick={() => handleSaveEdit(row.id)}
            className={cn(
              'p-1 rounded hover:bg-green-100 dark:hover:bg-green-900',
              'text-green-600 dark:text-green-400'
            )}
          >
            <FiCheck size={16} />
          </button>
          <button
            onClick={handleCancelEdit}
            className={cn(
              'p-1 rounded hover:bg-red-100 dark:hover:bg-red-900',
              'text-red-600 dark:text-red-400'
            )}
          >
            <FiX size={16} />
          </button>
        </div>
      )
    }

    return (
      <div className="flex items-center gap-1">
        {operations?.onUpdate && (
          <button
            onClick={() => handleStartEdit(row.id)}
            className={cn(
              'p-1 rounded hover:bg-blue-100 dark:hover:bg-blue-900',
              'text-blue-600 dark:text-blue-400'
            )}
          >
            <FiEdit2 size={16} />
          </button>
        )}
        {operations?.onDelete && (
          <button
            onClick={() => handleDelete(row.id)}
            className={cn(
              'p-1 rounded hover:bg-red-100 dark:hover:bg-red-900',
              'text-red-600 dark:text-red-400'
            )}
          >
            <FiTrash2 size={16} />
          </button>
        )}
      </div>
    )
  }

  // CRUD handlers
  const handleStartEdit = (id: string) => {
    const row = data.find(r => r.id === id)
    if (row) {
      setEditingRow(id)
      setEditedData(row)
    }
  }

  const handleCancelEdit = () => {
    setEditingRow(null)
    setEditedData({})
  }

  const handleSaveEdit = async (id: string) => {
    if (!operations?.onUpdate) return
    
    try {
      await operations.onUpdate(id, editedData)
      setEditingRow(null)
      setEditedData({})
    } catch (error) {
      console.error('Failed to update row:', error)
    }
  }

  const handleDelete = async (id: string) => {
    if (!operations?.onDelete) return
    
    if (confirm('Are you sure you want to delete this item?')) {
      try {
        await operations.onDelete(id)
      } catch (error) {
        console.error('Failed to delete row:', error)
      }
    }
  }

  const handleCreate = async () => {
    if (!operations?.onCreate) return
    
    try {
      await operations.onCreate(newRowData as Omit<T, 'id'>)
      setIsCreating(false)
      setNewRowData({})
    } catch (error) {
      console.error('Failed to create row:', error)
    }
  }

  // Table instance
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      pagination,
      globalFilter
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn,
    enableSorting: finalConfig.enableSorting,
    enableGlobalFilter: finalConfig.enableSearch
  })

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Header with search and actions */}
      <div className="flex items-center justify-between gap-4">
        {finalConfig.enableSearch && (
          <div className="relative flex-1 max-w-sm">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder={finalConfig.searchPlaceholder}
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className={cn(
                'w-full pl-10 pr-4 py-2 rounded-lg border',
                'focus:outline-none focus:ring-2 focus:ring-blue-500',
                finalConfig.enableGlassmorphism && 'backdrop-blur-sm',
                isDark 
                  ? 'bg-white/5 border-white/10 text-white placeholder-gray-400' 
                  : 'bg-white/70 border-gray-200/50 text-gray-900 placeholder-gray-500'
              )}
            />
          </div>
        )}
        
        {operations?.onCreate && (
          <button
            onClick={() => setIsCreating(true)}
            className={cn(
              'flex items-center gap-2 px-4 py-2 rounded-lg',
              'bg-blue-600 hover:bg-blue-700 text-white',
              'transition-colors duration-200'
            )}
          >
            <FiPlus size={16} />
            Add New
          </button>
        )}
      </div>

      {/* Table container with glassmorphism */}
      <div className={cn(
        'relative rounded-xl border overflow-hidden',
        finalConfig.enableGlassmorphism && [
          'backdrop-blur-md',
          isDark 
            ? 'bg-white/5 border-white/10' 
            : 'bg-white/70 border-gray-200/50'
        ],
        !finalConfig.enableGlassmorphism && [
          isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        ]
      )}>
        {loading && (
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className={cn(
                  'border-b',
                  isDark ? 'border-white/10' : 'border-gray-200/50'
                )}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className={cn(
                        'px-4 py-3 text-left text-sm font-semibold',
                        'cursor-pointer select-none',
                        isDark ? 'text-white/90' : 'text-gray-900/90'
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div className="flex items-center gap-2">
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getIsSorted() && (
                          <span className="text-blue-500">
                            {header.column.getIsSorted() === 'desc' ? '↓' : '↑'}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              <AnimatePresence>
                {table.getRowModel().rows.map((row) => (
                  <motion.tr
                    key={row.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={cn(
                      'border-b transition-colors duration-200',
                      'hover:bg-black/5 dark:hover:bg-white/5',
                      isDark ? 'border-white/10' : 'border-gray-200/50',
                      onRowClick && 'cursor-pointer'
                    )}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className={cn(
                          'px-4 py-3 text-sm',
                          isDark ? 'text-white/80' : 'text-gray-900/80'
                        )}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </motion.tr>
                ))}
              </AnimatePresence>
              
              {table.getRowModel().rows.length === 0 && !loading && (
                <tr>
                  <td
                    colSpan={tableColumns.length}
                    className={cn(
                      'px-4 py-8 text-center text-sm',
                      isDark ? 'text-white/60' : 'text-gray-500'
                    )}
                  >
                    {emptyMessage}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {finalConfig.enablePagination && (
        <div className="flex items-center justify-between">
          <div className={cn(
            'text-sm',
            isDark ? 'text-white/70' : 'text-gray-600'
          )}>
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} entries
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className={cn(
                'p-2 rounded-lg border transition-colors',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                isDark 
                  ? 'border-white/10 hover:bg-white/5' 
                  : 'border-gray-200 hover:bg-gray-50'
              )}
            >
              <FiChevronLeft size={16} />
            </button>
            
            <span className={cn(
              'px-3 py-1 text-sm',
              isDark ? 'text-white/70' : 'text-gray-600'
            )}>
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </span>
            
            <button
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className={cn(
                'p-2 rounded-lg border transition-colors',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                isDark 
                  ? 'border-white/10 hover:bg-white/5' 
                  : 'border-gray-200 hover:bg-gray-50'
              )}
            >
              <FiChevronRight size={16} />
            </button>
          </div>
        </div>
      )}

      {/* Create new row modal */}
      {isCreating && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className={cn(
            'rounded-xl p-6 w-full max-w-md mx-4 border shadow-xl',
            finalConfig.enableGlassmorphism && [
              'backdrop-blur-md',
              isDark
                ? 'bg-white/10 border-white/20'
                : 'bg-white/90 border-gray-200/50'
            ],
            !finalConfig.enableGlassmorphism && [
              isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            ]
          )}>
            <h3 className={cn(
              'text-lg font-semibold mb-4',
              isDark ? 'text-white' : 'text-gray-900'
            )}>
              Add New Item
            </h3>
            <div className="space-y-4">
              {columns.filter(col => col.editable).map((col) => (
                <div key={String(col.key)}>
                  <label className={cn(
                    'block text-sm font-medium mb-1',
                    isDark ? 'text-white/90' : 'text-gray-700'
                  )}>
                    {col.title}
                    {col.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  {renderEditCell(col, newRowData[col.key], (value) => {
                    setNewRowData(prev => ({ ...prev, [col.key]: value }))
                  })}
                </div>
              ))}
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => {
                  setIsCreating(false)
                  setNewRowData({})
                }}
                className={cn(
                  'px-4 py-2 rounded-lg transition-colors',
                  isDark
                    ? 'text-gray-300 hover:bg-white/10'
                    : 'text-gray-600 hover:bg-gray-100'
                )}
              >
                Cancel
              </button>
              <button
                onClick={handleCreate}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EnhancedModernTable
