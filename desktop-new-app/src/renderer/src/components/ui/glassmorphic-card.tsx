'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'motion/react'
import { cn } from '../../lib/aceternity-utils'
import { useThemeContext } from '../../context/useThemeContext'
import { GlowingEffect } from './glowing-effect'
import { TracingBeam } from './tracing-beam'

interface GlassmorphicCardProps {
  children: React.ReactNode
  className?: string
  containerClassName?: string
  enableGlow?: boolean
  enableTracingBeam?: boolean
  enable3D?: boolean
  glowIntensity?: 'low' | 'medium' | 'high'
  blurIntensity?: 'low' | 'medium' | 'high'
}

const blurLevels = {
  low: 'backdrop-blur-sm',
  medium: 'backdrop-blur-md',
  high: 'backdrop-blur-lg'
}

const glowSpreads = {
  low: 15,
  medium: 25,
  high: 35
}

export const GlassmorphicCard: React.FC<GlassmorphicCardProps> = ({
  children,
  className,
  containerClassName,
  enableGlow = true,
  enableTracingBeam = false,
  enable3D = false,
  glowIntensity = 'medium',
  blurIntensity = 'medium'
}) => {
  const { isDark } = useThemeContext()
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // 3D effect handlers
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!enable3D || !cardRef.current) return
    
    const rect = cardRef.current.getBoundingClientRect()
    const x = (e.clientX - rect.left - rect.width / 2) / 25
    const y = (e.clientY - rect.top - rect.height / 2) / 25
    
    cardRef.current.style.transform = `perspective(1000px) rotateY(${x}deg) rotateX(${y}deg)`
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    if (enable3D && cardRef.current) {
      cardRef.current.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg)'
    }
  }

  const cardContent = (
    <motion.div
      ref={cardRef}
      className={cn(
        'relative group',
        // Base glassmorphism styles
        'rounded-xl border',
        isDark 
          ? 'bg-white/5 border-white/10' 
          : 'bg-white/70 border-gray-200/50',
        blurLevels[blurIntensity],
        // Shadow effects
        'shadow-xl',
        isDark ? 'shadow-black/20' : 'shadow-gray-900/10',
        // Transitions
        'transition-all duration-300 ease-out',
        // Hover effects
        'hover:shadow-2xl',
        isDark 
          ? 'hover:bg-white/10 hover:border-white/20' 
          : 'hover:bg-white/80 hover:border-gray-300/60',
        // 3D transform styles
        enable3D && 'transform-gpu',
        className
      )}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Glowing effect */}
      {enableGlow && (
        <GlowingEffect
          blur={8}
          spread={glowSpreads[glowIntensity]}
          proximity={0.4}
          glow={isHovered}
          disabled={!isHovered}
          className="absolute inset-0 rounded-xl"
        />
      )}

      {/* Gradient overlay for enhanced glassmorphism */}
      <div className={cn(
        'absolute inset-0 rounded-xl pointer-events-none',
        'bg-gradient-to-br opacity-30',
        isDark 
          ? 'from-white/10 via-transparent to-white/5' 
          : 'from-white/30 via-transparent to-white/10'
      )} />

      {/* Animated border glow on hover */}
      <div className={cn(
        'absolute inset-0 rounded-xl pointer-events-none transition-opacity duration-300',
        'bg-gradient-to-r',
        isDark 
          ? 'from-cyan-500/20 via-blue-500/10 to-purple-500/20' 
          : 'from-blue-500/20 via-indigo-500/10 to-purple-500/20',
        isHovered ? 'opacity-100' : 'opacity-0'
      )} />

      {/* Content container with proper text contrast */}
      <div className={cn(
        'relative z-10 p-6',
        // Ensure text readability
        isDark ? 'text-white/90' : 'text-gray-900/90',
        // Text shadow for better contrast
        'text-shadow-sm'
      )}>
        {children}
      </div>

      {/* Subtle inner highlight */}
      <div className={cn(
        'absolute top-0 left-0 right-0 h-px rounded-t-xl',
        isDark ? 'bg-white/20' : 'bg-white/40'
      )} />
    </motion.div>
  )

  // Wrap with tracing beam if enabled
  if (enableTracingBeam) {
    return (
      <div className={cn('w-full', containerClassName)}>
        <TracingBeam>
          {cardContent}
        </TracingBeam>
      </div>
    )
  }

  return (
    <div className={cn('w-full', containerClassName)}>
      {cardContent}
    </div>
  )
}

// Enhanced version of the regular card with glassmorphism
export const GlassmorphicCardSimple: React.FC<{
  children: React.ReactNode
  className?: string
  enableHover?: boolean
}> = ({ children, className, enableHover = true }) => {
  const { isDark } = useThemeContext()
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4',
        // Glassmorphism base
        isDark 
          ? 'bg-white/5 border-white/10 backdrop-blur-sm' 
          : 'bg-white/60 border-gray-200/40 backdrop-blur-sm',
        'shadow-lg',
        isDark ? 'shadow-black/10' : 'shadow-gray-900/5',
        // Hover effects
        enableHover && [
          'transition-all duration-300 cursor-pointer',
          'hover:shadow-xl',
          isDark 
            ? 'hover:bg-white/10 hover:border-white/20' 
            : 'hover:bg-white/70 hover:border-gray-300/50'
        ],
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Hover glow */}
      {enableHover && isHovered && (
        <div className={cn(
          'absolute inset-0 rounded-lg pointer-events-none',
          'bg-gradient-to-r opacity-20',
          isDark 
            ? 'from-cyan-500/30 to-blue-500/30' 
            : 'from-blue-500/30 to-indigo-500/30'
        )} />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default GlassmorphicCard
