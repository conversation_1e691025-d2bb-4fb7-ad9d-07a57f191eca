import React from 'react'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'

interface ModernTableProps {
  children: React.ReactNode
  className?: string
}

interface ModernTableHeaderProps {
  children: React.ReactNode
  className?: string
}

interface ModernTableBodyProps {
  children: React.ReactNode
  className?: string
}

interface ModernTableRowProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
  isSelected?: boolean
}

interface ModernTableCellProps {
  children: React.ReactNode
  className?: string
  isHeader?: boolean
}

interface ModernTableActionButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'danger' | 'success'
  size?: 'sm' | 'md'
  disabled?: boolean
  className?: string
}

export const ModernTable: React.FC<ModernTableProps> = ({ children, className }) => {
  const { isDark } = useThemeContext()
  
  return (
    <div className={cn(
      "w-full overflow-hidden rounded-xl border backdrop-blur-sm",
      isDark 
        ? "border-white/10 bg-white/5" 
        : "border-gray-200/50 bg-white/70",
      className
    )}>
      <div className="overflow-x-auto">
        <table className="w-full">
          {children}
        </table>
      </div>
    </div>
  )
}

export const ModernTableHeader: React.FC<ModernTableHeaderProps> = ({ children, className }) => {
  const { isDark } = useThemeContext()
  
  return (
    <thead className={cn(
      "border-b",
      isDark 
        ? "border-white/10 bg-white/5" 
        : "border-gray-200/50 bg-gray-50/70",
      className
    )}>
      {children}
    </thead>
  )
}

export const ModernTableBody: React.FC<ModernTableBodyProps> = ({ children, className }) => {
  return (
    <tbody className={cn("divide-y divide-border/50", className)}>
      {children}
    </tbody>
  )
}

export const ModernTableRow: React.FC<ModernTableRowProps> = ({ 
  children, 
  className, 
  onClick, 
  isSelected = false 
}) => {
  const { isDark } = useThemeContext()
  
  return (
    <tr 
      className={cn(
        "transition-all duration-200",
        onClick && "cursor-pointer",
        isSelected && (isDark ? "bg-blue-500/20" : "bg-blue-50/70"),
        !isSelected && (isDark 
          ? "hover:bg-white/5" 
          : "hover:bg-gray-50/70"
        ),
        className
      )}
      onClick={onClick}
    >
      {children}
    </tr>
  )
}

export const ModernTableCell: React.FC<ModernTableCellProps> = ({ 
  children, 
  className, 
  isHeader = false 
}) => {
  const { isDark } = useThemeContext()
  
  const Component = isHeader ? 'th' : 'td'
  
  return (
    <Component className={cn(
      "px-4 py-3 text-left",
      isHeader && (isDark 
        ? "text-sm font-semibold text-white/90" 
        : "text-sm font-semibold text-gray-900"
      ),
      !isHeader && (isDark 
        ? "text-sm text-white/80" 
        : "text-sm text-gray-700"
      ),
      className
    )}>
      {children}
    </Component>
  )
}

export const ModernTableActionButton: React.FC<ModernTableActionButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'sm',
  disabled = false,
  className
}) => {
  const { isDark } = useThemeContext()
  
  const baseStyles = cn(
    "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:opacity-50 disabled:cursor-not-allowed",
    "hover:scale-105 active:scale-95"
  )
  
  const sizeStyles = {
    sm: "px-3 py-1.5 text-xs",
    md: "px-4 py-2 text-sm"
  }
  
  const variantStyles = {
    primary: isDark 
      ? "bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500" 
      : "bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-500",
    secondary: isDark 
      ? "bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500" 
      : "bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-500",
    danger: isDark 
      ? "bg-red-600 hover:bg-red-700 text-white focus:ring-red-500" 
      : "bg-red-500 hover:bg-red-600 text-white focus:ring-red-500",
    success: isDark 
      ? "bg-green-600 hover:bg-green-700 text-white focus:ring-green-500" 
      : "bg-green-500 hover:bg-green-600 text-white focus:ring-green-500"
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseStyles,
        sizeStyles[size],
        variantStyles[variant],
        className
      )}
    >
      {children}
    </button>
  )
}

// Pagination component for tables
interface ModernTablePaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  pageSize: number
  onPageSizeChange: (size: number) => void
  totalItems: number
  className?: string
}

export const ModernTablePagination: React.FC<ModernTablePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
  totalItems,
  className
}) => {
  const { isDark } = useThemeContext()
  
  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalItems)
  
  return (
    <div className={cn(
      "flex items-center justify-between px-4 py-3 border-t",
      isDark 
        ? "border-white/10 bg-white/5" 
        : "border-gray-200/50 bg-gray-50/70",
      className
    )}>
      <div className="flex items-center space-x-2">
        <span className={cn(
          "text-sm",
          isDark ? "text-white/70" : "text-gray-600"
        )}>
          Showing {startItem} to {endItem} of {totalItems} results
        </span>
        
        <select
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          className={cn(
            "ml-2 rounded-md border px-2 py-1 text-sm",
            isDark 
              ? "border-white/20 bg-white/10 text-white" 
              : "border-gray-300 bg-white text-gray-900"
          )}
        >
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
          <option value={100}>100</option>
        </select>
      </div>
      
      <div className="flex items-center space-x-1">
        <ModernTableActionButton
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          variant="secondary"
          size="sm"
        >
          Previous
        </ModernTableActionButton>
        
        {/* Page numbers */}
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
          if (page > totalPages) return null
          
          return (
            <ModernTableActionButton
              key={page}
              onClick={() => onPageChange(page)}
              variant={page === currentPage ? "primary" : "secondary"}
              size="sm"
            >
              {page}
            </ModernTableActionButton>
          )
        })}
        
        <ModernTableActionButton
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          variant="secondary"
          size="sm"
        >
          Next
        </ModernTableActionButton>
      </div>
    </div>
  )
}
