import * as React from 'react'
import { cn } from '../../lib/aceternity-utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'destructive' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    const baseStyles = cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200",
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
      "disabled:pointer-events-none disabled:opacity-50",
      "hover:scale-105 active:scale-95",
      "backdrop-blur-sm border"
    )

    const variants = {
      default: cn(
        "bg-blue-600 hover:bg-blue-700 text-white border-blue-600",
        "dark:bg-blue-500 dark:hover:bg-blue-600 dark:border-blue-500",
        "shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40",
        "focus-visible:ring-blue-500"
      ),
      outline: cn(
        "border-gray-300 bg-white/70 text-gray-700 hover:bg-gray-50/80",
        "dark:border-white/20 dark:bg-white/10 dark:text-white dark:hover:bg-white/20",
        "focus-visible:ring-gray-500"
      ),
      destructive: cn(
        "bg-red-600 hover:bg-red-700 text-white border-red-600",
        "dark:bg-red-500 dark:hover:bg-red-600 dark:border-red-500",
        "shadow-lg shadow-red-500/25 hover:shadow-red-500/40",
        "focus-visible:ring-red-500"
      ),
      secondary: cn(
        "bg-gray-600 hover:bg-gray-700 text-white border-gray-600",
        "dark:bg-gray-500 dark:hover:bg-gray-600 dark:border-gray-500",
        "shadow-lg shadow-gray-500/25 hover:shadow-gray-500/40",
        "focus-visible:ring-gray-500"
      ),
      ghost: cn(
        "border-transparent bg-transparent text-gray-700 hover:bg-gray-100/70",
        "dark:text-white dark:hover:bg-white/10",
        "focus-visible:ring-gray-500"
      ),
      link: cn(
        "border-transparent bg-transparent text-blue-600 underline-offset-4 hover:underline",
        "dark:text-blue-400",
        "focus-visible:ring-blue-500"
      )
    }

    const sizes = {
      default: "h-10 px-4 py-2",
      sm: "h-8 px-3 text-xs",
      lg: "h-12 px-8 text-base",
      icon: "h-10 w-10"
    }

    return (
      <button
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button }
