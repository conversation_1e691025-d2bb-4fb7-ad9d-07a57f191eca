import React from 'react'
import { motion } from 'motion/react'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

interface LampBackgroundProps {
  children: React.ReactNode
  className?: string
}

const LampBackground: React.FC<LampBackgroundProps> = ({ children, className }) => {
  const { isDark } = useThemeContext()

  return (
    <div
      className={cn(
        'relative flex min-h-screen flex-col overflow-hidden w-full z-0',
        isDark ? 'bg-slate-950' : 'bg-background',
        className
      )}
    >
      {/* Enhanced Aceternity Lamp positioned at top with increased light area and intensity */}
      <div className="relative flex w-full items-start justify-center isolate z-0 pt-2">
        <motion.div
          initial={{ opacity: 0.3, width: '30rem' }}
          whileInView={{ opacity: 1, width: '80rem' }}
          transition={{
            delay: 0.2,
            duration: 1.2,
            ease: 'easeInOut'
          }}
          style={{
            backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`
          }}
          className={cn(
            "absolute inset-auto right-1/2 h-96 overflow-visible w-[80rem] bg-gradient-conic text-white [--conic-position:from_60deg_at_center_top]",
            isDark
              ? "from-emerald-400 via-cyan-500 to-transparent"
              : "from-green-400 via-blue-500 to-transparent"
          )}
        >
          <div className={cn(
            "absolute w-[100%] left-0 h-60 bottom-0 z-20 [mask-image:linear-gradient(to_top,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
          <div className={cn(
            "absolute w-60 h-[100%] left-0 bottom-0 z-20 [mask-image:linear-gradient(to_right,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
        </motion.div>

        <motion.div
          initial={{ opacity: 0.3, width: '30rem' }}
          whileInView={{ opacity: 1, width: '80rem' }}
          transition={{
            delay: 0.4,
            duration: 1.2,
            ease: 'easeInOut'
          }}
          style={{
            backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`
          }}
          className={cn(
            "absolute inset-auto left-1/2 h-96 w-[80rem] bg-gradient-conic text-white [--conic-position:from_300deg_at_center_top]",
            isDark
              ? "from-transparent via-transparent to-emerald-400"
              : "from-transparent via-transparent to-green-400"
          )}
        >
          <div className={cn(
            "absolute w-60 h-[100%] right-0 bottom-0 z-20 [mask-image:linear-gradient(to_left,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
          <div className={cn(
            "absolute w-[100%] right-0 h-60 bottom-0 z-20 [mask-image:linear-gradient(to_top,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
        </motion.div>

        {/* Enhanced glowing effects */}
        <div className={cn(
          "absolute top-40 h-80 w-full scale-x-150 blur-3xl",
          isDark ? "bg-slate-950" : "bg-background"
        )}></div>

        <div className="absolute top-40 z-50 h-80 w-full bg-transparent opacity-15 backdrop-blur-md"></div>

        {/* Multiple layered glow effects for increased intensity */}
        <div className={cn(
          "absolute inset-auto z-50 h-64 w-[60rem] top-16 rounded-full opacity-40 blur-3xl",
          isDark ? "bg-emerald-500" : "bg-green-400"
        )}></div>

        <div className={cn(
          "absolute inset-auto z-40 h-48 w-[80rem] top-20 rounded-full opacity-30 blur-2xl",
          isDark ? "bg-cyan-400" : "bg-blue-400"
        )}></div>

        <div className={cn(
          "absolute inset-auto z-30 h-32 w-[100rem] top-24 rounded-full opacity-20 blur-xl",
          isDark ? "bg-teal-400" : "bg-teal-300"
        )}></div>

        <motion.div
          initial={{ width: '12rem' }}
          whileInView={{ width: '24rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          className={cn(
            "absolute inset-auto z-30 h-48 w-96 top-12 rounded-full blur-2xl",
            isDark ? "bg-cyan-400" : "bg-blue-300"
          )}
        ></motion.div>

        <motion.div
          initial={{ width: '20rem' }}
          whileInView={{ width: '40rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          className={cn(
            "absolute inset-auto z-50 h-0.5 w-[40rem] top-6",
            isDark ? "bg-cyan-400" : "bg-blue-400"
          )}
        ></motion.div>

        <div className={cn(
          "absolute inset-auto z-40 h-56 w-full top-0",
          isDark ? "bg-slate-950" : "bg-background"
        )}></div>
      </div>

      {/* Content with glassmorphic effect and minimal top padding */}
      <div className="relative z-50 flex flex-col px-5 pt-4">
        {children}
      </div>
    </div>
  )
}

export default LampBackground
