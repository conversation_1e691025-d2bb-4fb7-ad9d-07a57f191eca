import React, { useState, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { EnhancedModernTable, EnhancedColumnDef, CRUDOperations } from './ui/enhanced-modern-table'
import { ComplaintData } from '../../../shared/api'
import { FiEye, FiFileText, FiBarChart, FiTrash2 } from 'react-icons/fi'
import { cn } from '../lib/aceternity-utils'
import { useThemeContext } from '../context/useThemeContext'

// Transform ComplaintData to table row format
interface ComplaintTableRow {
  id: string
  complaint_number: string
  complainant_name: string
  category_of_fraud: string
  date_of_complaint: string
  amount: number
  status: string
  file_name?: string
  fraud_type?: string
  created_at: string
  updated_at: string
  actions?: never // Add actions property to fix TypeScript error
}

interface EnhancedComplaintTableProps {
  complaints: ComplaintData[]
  loading?: boolean
  onRefresh?: () => void
}

const EnhancedComplaintTable: React.FC<EnhancedComplaintTableProps> = ({
  complaints,
  loading = false,
  onRefresh
}) => {
  const navigate = useNavigate()
  const { isDark } = useThemeContext()

  // Delete confirmation state
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean
    complaintId: string
    complaintNumber: string
  }>({ show: false, complaintId: '', complaintNumber: '' })
  const [isDeleting, setIsDeleting] = useState(false)

  // Handle delete complaint
  const handleDeleteComplaint = useCallback(
    async (complaintId: string): Promise<void> => {
      setIsDeleting(true)
      try {
        const result = await window.api.database.deleteComplaint(complaintId)
        if (result) {
          onRefresh?.()
          setDeleteConfirm({ show: false, complaintId: '', complaintNumber: '' })
        } else {
          console.error('Failed to delete complaint')
        }
      } catch (error) {
        console.error('Error deleting complaint:', error)
      } finally {
        setIsDeleting(false)
      }
    },
    [onRefresh]
  )

  // Transform complaints data for table
  const tableData = useMemo((): ComplaintTableRow[] => {
    return complaints.map((complaint) => ({
      id: complaint.id,
      complaint_number:
        (complaint.metadata?.complaint_number as string) || complaint.id.slice(0, 8),
      complainant_name: (complaint.metadata?.complainant_name as string) || 'Unknown',
      category_of_fraud:
        (complaint.metadata?.subcategory as string) || complaint.fraud_type || 'Unknown',
      date_of_complaint: (complaint.metadata?.date as string) || complaint.created_at,
      amount: parseFloat(String(complaint.metadata?.total_amount || 0).replace(/,/g, '')),
      status: complaint.status || 'processed',
      file_name: complaint.file_name,
      fraud_type: complaint.fraud_type,
      created_at: complaint.created_at,
      updated_at: complaint.updated_at
    }))
  }, [complaints])

  // Define table columns
  const columns: EnhancedColumnDef<ComplaintTableRow>[] = useMemo(
    () => [
      {
        key: 'complaint_number',
        title: 'Complaint Number',
        type: 'text',
        editable: true,
        required: true,
        width: 150
      },
      {
        key: 'complainant_name',
        title: 'Complainant Name',
        type: 'text',
        editable: true,
        required: true,
        width: 200
      },
      {
        key: 'category_of_fraud',
        title: 'Category of Fraud',
        type: 'select',
        editable: true,
        options: [
          { value: 'banking_upi', label: 'Banking UPI' },
          { value: 'credit_card', label: 'Credit Card' },
          { value: 'debit_card', label: 'Debit Card' },
          { value: 'net_banking', label: 'Net Banking' },
          { value: 'mobile_banking', label: 'Mobile Banking' },
          { value: 'other', label: 'Other' }
        ],
        width: 180
      },
      {
        key: 'amount',
        title: 'Amount',
        type: 'number',
        editable: true,
        render: (value: number) => (
          <span
            className={cn(
              'font-medium',
              value > 100000
                ? 'text-red-600 dark:text-red-400'
                : 'text-green-600 dark:text-green-400'
            )}
          >
            ₹{value.toLocaleString('en-IN')}
          </span>
        ),
        width: 120
      },
      {
        key: 'date_of_complaint',
        title: 'Date',
        type: 'date',
        editable: true,
        render: (value: string) => new Date(value).toLocaleDateString('en-IN'),
        width: 120
      },
      {
        key: 'status',
        title: 'Status',
        type: 'select',
        editable: true,
        options: [
          { value: 'processed', label: 'Processed' },
          { value: 'pending', label: 'Pending' },
          { value: 'completed', label: 'Completed' },
          { value: 'rejected', label: 'Rejected' }
        ],
        render: (value: string) => (
          <span
            className={cn('px-2 py-1 rounded-full text-xs font-medium', {
              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200':
                value === 'completed',
              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200':
                value === 'processed',
              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200':
                value === 'pending',
              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': value === 'rejected'
            })}
          >
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </span>
        ),
        width: 100
      },
      {
        key: 'actions',
        title: 'Actions',
        type: 'custom',
        render: (_, row: ComplaintTableRow) => (
          <div className="flex items-center gap-1">
            <button
              onClick={(e) => {
                e.stopPropagation()
                navigate(`/complaint-details/${row.id}`)
              }}
              className={cn(
                'p-2 rounded-lg transition-colors',
                'hover:bg-blue-100 dark:hover:bg-blue-900',
                'text-blue-600 dark:text-blue-400'
              )}
              title="View Details"
            >
              <FiEye size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                navigate(`/graph-visualization/${row.id}`)
              }}
              className={cn(
                'p-2 rounded-lg transition-colors',
                'hover:bg-green-100 dark:hover:bg-green-900',
                'text-green-600 dark:text-green-400'
              )}
              title="Graph Visualization"
            >
              <FiBarChart size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                navigate(`/complaint-details/${row.id}`)
              }}
              className={cn(
                'p-2 rounded-lg transition-colors',
                'hover:bg-purple-100 dark:hover:bg-purple-900',
                'text-purple-600 dark:text-purple-400'
              )}
              title="Generate Notice"
            >
              <FiFileText size={16} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                setDeleteConfirm({
                  show: true,
                  complaintId: row.id,
                  complaintNumber: row.complaint_number
                })
              }}
              className={cn(
                'p-2 rounded-lg transition-colors',
                'hover:bg-red-100 dark:hover:bg-red-900',
                'text-red-600 dark:text-red-400'
              )}
              title="Delete Complaint"
            >
              <FiTrash2 size={16} />
            </button>
          </div>
        ),
        width: 160
      }
    ],
    [navigate]
  )

  // CRUD operations
  const operations: CRUDOperations<ComplaintTableRow> = {
    onCreate: async (data) => {
      try {
        // Create new complaint in database
        const newComplaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'> = {
          title: `Complaint - ${data.complaint_number}`,
          description: `Fraud complaint for ${data.complainant_name}`,
          file_name: '',
          fraud_type: data.category_of_fraud,
          extraction_type: 'manual',
          max_layer: 7,
          metadata: {
            complaint_number: data.complaint_number,
            complainant_name: data.complainant_name,
            subcategory: data.category_of_fraud,
            total_amount: data.amount,
            date: data.date_of_complaint
          },
          transactions: [],
          layer_transactions: {},
          bank_notice_data: {},
          graph_data: { nodes: [], edges: [], transactions: [], metadata: {}, max_layer: 7 },
          extraction_info: {},
          status: data.status
        }

        const id = await window.api.database.storeComplaint(newComplaint)
        onRefresh?.()

        return {
          ...data,
          id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      } catch (error) {
        console.error('Failed to create complaint:', error)
        throw error
      }
    },

    onUpdate: async (id, data) => {
      try {
        // Get current complaint
        const currentComplaint = complaints.find((c) => c.id === id)
        if (!currentComplaint) throw new Error('Complaint not found')

        // Update metadata
        const updatedMetadata = {
          ...currentComplaint.metadata,
          complaint_number: data.complaint_number,
          complainant_name: data.complainant_name,
          subcategory: data.category_of_fraud,
          total_amount: data.amount,
          date: data.date_of_complaint
        }

        const updateData = {
          metadata: updatedMetadata,
          status: data.status,
          fraud_type: data.category_of_fraud
        }

        await window.api.database.updateComplaint(id, updateData)
        onRefresh?.()

        return {
          ...tableData.find((row) => row.id === id)!,
          ...data,
          updated_at: new Date().toISOString()
        }
      } catch (error) {
        console.error('Failed to update complaint:', error)
        throw error
      }
    },

    onDelete: async (id) => {
      try {
        await window.api.database.deleteComplaint(id)
        onRefresh?.()
      } catch (error) {
        console.error('Failed to delete complaint:', error)
        throw error
      }
    }
  }

  // Handle row click to navigate to details
  const handleRowClick = useCallback(
    (row: ComplaintTableRow) => {
      navigate(`/complaint-details/${row.id}`)
    },
    [navigate]
  )

  return (
    <>
      <EnhancedModernTable
        data={tableData}
        columns={columns}
        operations={operations}
        loading={loading}
        onRowClick={handleRowClick}
        config={{
          enableSearch: true,
          enablePagination: true,
          enableSorting: true,
          enableSelection: false,
          enableGlassmorphism: true,
          enableRowActions: false, // We have custom actions column
          pageSize: 15,
          searchPlaceholder: 'Search complaints...'
        }}
        emptyMessage="No complaints found. Upload a complaint file to get started."
        className="w-full"
      />

      {/* Delete Confirmation Dialog */}
      {deleteConfirm.show && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div
            className={cn(
              'p-6 rounded-lg backdrop-blur-md border shadow-lg max-w-md w-full mx-4',
              isDark
                ? 'bg-white/10 border-white/10 text-white'
                : 'bg-white/90 border-gray-200/50 text-gray-900'
            )}
          >
            <h3
              className={cn('text-lg font-semibold mb-4', isDark ? 'text-white' : 'text-gray-900')}
            >
              Delete Complaint
            </h3>
            <p className={cn('mb-6 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
              Are you sure you want to delete complaint &quot;{deleteConfirm.complaintNumber}&quot;?
              This action cannot be undone.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() =>
                  setDeleteConfirm({ show: false, complaintId: '', complaintNumber: '' })
                }
                disabled={isDeleting}
                className={cn(
                  'px-4 py-2 rounded-lg font-semibold transition-colors',
                  isDark
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                )}
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteComplaint(deleteConfirm.complaintId)}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default EnhancedComplaintTable
