import React, { useEffect, useState } from 'react'
import { Circle, Popup } from 'react-leaflet'
import { Hotspot, hotspotAnalysisService } from '../../services/hotspotAnalysisService'
import { ComplaintData } from '../../../../shared/api'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'
import { AlertTriangle, TrendingUp, MapPin, Activity } from 'lucide-react'

interface HotspotVisualizationProps {
  complaints: ComplaintData[]
  selectedComplaint?: string | null
  analysisType?: 'bank' | 'fraudster' | 'combined'
  onHotspotSelect?: (hotspot: Hotspot | null) => void
}

const HotspotVisualization: React.FC<HotspotVisualizationProps> = ({
  complaints,
  selectedComplaint,
  analysisType = 'combined',
  onHotspotSelect
}) => {
  const { isDark } = useThemeContext()
  const [hotspots, setHotspots] = useState<Hotspot[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [recommendations, setRecommendations] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null)

  // Analyze hotspots when data changes
  useEffect(() => {
    const analyzeHotspots = async () => {
      try {
        setLoading(true)

        // Filter complaints based on selection
        const complaintsToAnalyze = selectedComplaint
          ? complaints.filter((c) => c.id === selectedComplaint)
          : complaints

        const result = await hotspotAnalysisService.analyzeHotspots(complaintsToAnalyze, {
          radiusKm: 50,
          minPointsForHotspot: 3,
          analysisType
        })

        setHotspots(result.hotspots)
        setStatistics(result.statistics)
        setRecommendations(result.recommendations)
      } catch (error) {
        console.error('Error analyzing hotspots:', error)
      } finally {
        setLoading(false)
      }
    }

    analyzeHotspots()
  }, [complaints, selectedComplaint, analysisType])

  // Handle hotspot selection
  const handleHotspotClick = (hotspot: Hotspot) => {
    setSelectedHotspot(hotspot)
    onHotspotSelect?.(hotspot)
  }

  // Get color for hotspot based on risk level
  const getHotspotColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'critical':
        return '#dc2626'
      case 'high':
        return '#ef4444'
      case 'medium':
        return '#f59e0b'
      case 'low':
        return '#22c55e'
      default:
        return '#6b7280'
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-sm">Analyzing hotspots...</span>
      </div>
    )
  }

  return (
    <>
      {/* Render hotspot circles on map */}
      {hotspots.map((hotspot) => (
        <Circle
          key={hotspot.id}
          center={[hotspot.center.latitude, hotspot.center.longitude]}
          radius={hotspot.radius * 1000} // Convert km to meters
          pathOptions={{
            color: getHotspotColor(hotspot.riskLevel),
            fillColor: getHotspotColor(hotspot.riskLevel),
            fillOpacity: 0.2 + hotspot.intensity * 0.3,
            weight: 2,
            opacity: 0.8
          }}
          eventHandlers={{
            click: () => handleHotspotClick(hotspot)
          }}
        >
          <Popup maxWidth={400}>
            <div className="p-3 min-w-[350px]">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-bold text-lg text-gray-800 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-orange-500" />
                  Fraud Hotspot #{hotspot.id.split('_')[1]}
                </h3>
                <span
                  className="px-2 py-1 rounded-full text-xs font-semibold text-white"
                  style={{ backgroundColor: getHotspotColor(hotspot.riskLevel) }}
                >
                  {hotspot.riskLevel.toUpperCase()} RISK
                </span>
              </div>

              <div className="space-y-3 text-sm">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {hotspot.transactionCount}
                    </div>
                    <div className="text-xs text-gray-500">Transactions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">
                      {formatCurrency(hotspot.totalAmount)}
                    </div>
                    <div className="text-xs text-gray-500">Total Amount</div>
                  </div>
                </div>

                {/* Analysis Details */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Intensity:</span>
                    <span className="font-semibold">{(hotspot.intensity * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Radius:</span>
                    <span className="font-semibold">{hotspot.radius.toFixed(1)} km</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Amount:</span>
                    <span className="font-semibold">
                      {formatCurrency(hotspot.analysis.averageAmount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Amount:</span>
                    <span className="font-semibold">
                      {formatCurrency(hotspot.analysis.maxAmount)}
                    </span>
                  </div>
                </div>

                {/* Fraud Types */}
                {hotspot.analysis.fraudTypes.length > 0 && (
                  <div>
                    <div className="text-gray-600 text-xs mb-1">Fraud Types:</div>
                    <div className="flex flex-wrap gap-1">
                      {hotspot.analysis.fraudTypes.map((type, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                        >
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Banks Involved */}
                <div className="flex justify-between">
                  <span className="text-gray-600">Banks Involved:</span>
                  <span className="font-semibold">{hotspot.analysis.bankCount}</span>
                </div>

                {/* Time Span */}
                <div className="flex justify-between">
                  <span className="text-gray-600">Time Span:</span>
                  <span className="font-semibold">{hotspot.analysis.timeSpan}</span>
                </div>
              </div>
            </div>
          </Popup>
        </Circle>
      ))}

      {/* Hotspot Analysis Panel */}
      {statistics && (
        <div className="absolute top-4 right-4 z-[1000]">
          <div
            className={cn(
              'p-4 rounded-xl border backdrop-blur-md shadow-lg max-w-sm',
              isDark
                ? 'bg-white/10 border-white/20 text-white'
                : 'bg-white/90 border-gray-200/50 text-gray-900'
            )}
          >
            <div className="flex items-center gap-2 mb-3">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              <h3 className="font-semibold">Hotspot Analysis</h3>
            </div>

            {/* Statistics */}
            <div className="space-y-2 text-sm mb-4">
              <div className="flex justify-between">
                <span>Total Hotspots:</span>
                <span className="font-semibold">{statistics.totalHotspots}</span>
              </div>
              <div className="flex justify-between">
                <span>Critical Risk:</span>
                <span className="font-semibold text-red-600">{statistics.criticalHotspots}</span>
              </div>
              <div className="flex justify-between">
                <span>High Risk:</span>
                <span className="font-semibold text-orange-600">{statistics.highRiskHotspots}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Amount:</span>
                <span className="font-semibold">
                  {formatCurrency(statistics.totalAmountInHotspots)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Avg Intensity:</span>
                <span className="font-semibold">
                  {(statistics.averageHotspotIntensity * 100).toFixed(1)}%
                </span>
              </div>
            </div>

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="w-4 h-4 text-amber-500" />
                  <span className="font-semibold text-xs">Recommendations:</span>
                </div>
                <div className="space-y-1">
                  {recommendations.slice(0, 3).map((rec, idx) => (
                    <div
                      key={idx}
                      className="text-xs p-2 bg-amber-50 text-amber-800 rounded border-l-2 border-amber-400"
                    >
                      {rec}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default HotspotVisualization
