import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import MarkerClusterGroup from 'react-leaflet-cluster'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { ComplaintData, GraphNode } from '../../../../shared/api'
import { ifscEnrichmentService } from '../../services/ifscEnrichmentService'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'
import HotspotVisualization from './HotspotVisualization'

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'
})

interface FraudsterLocation {
  id: string
  type: 'suspect' | 'victim' | 'intermediary'
  name: string
  address: string
  accountNumber?: string
  layer: number
  latitude: number
  longitude: number
  transactionCount: number
  totalAmount: number
  nodeData: any
  connections: string[] // IDs of connected locations
}

interface FraudsterLocationMapProps {
  complaints: ComplaintData[]
  selectedComplaint?: string | null
  className?: string
  showHotspots?: boolean
}

const FraudsterLocationMap: React.FC<FraudsterLocationMapProps> = ({
  complaints,
  selectedComplaint,
  className,
  showHotspots = true
}) => {
  const { isDark } = useThemeContext()
  const [fraudsterLocations, setFraudsterLocations] = useState<FraudsterLocation[]>([])
  const [connections, setConnections] = useState<
    Array<{ from: [number, number]; to: [number, number]; amount: number }>
  >([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const mapRef = useRef<L.Map | null>(null)

  // Process complaints to extract fraudster locations
  useEffect(() => {
    const processFraudsterLocations = async () => {
      try {
        setLoading(true)
        setError(null)

        // Filter complaints based on selection
        const complaintsToProcess = selectedComplaint
          ? complaints.filter((c) => c.id === selectedComplaint)
          : complaints

        const allLocations: FraudsterLocation[] = []
        const allConnections: Array<{
          from: [number, number]
          to: [number, number]
          amount: number
        }> = []

        for (const complaint of complaintsToProcess) {
          if (complaint.graph_data?.nodes) {
            console.log(
              `Processing complaint ${complaint.id} with ${complaint.graph_data.nodes.length} nodes`
            )
            for (const node of complaint.graph_data.nodes) {
              console.log(`Processing node ${node.id}:`, {
                label: node.data.label,
                layer: node.data.layer,
                account_number: node.data.account_number,
                available_fields: Object.keys(node.data)
              })

              // Extract address information from node data
              const address = extractAddressFromNode(node)
              console.log(`Extracted address for node ${node.id}: "${address}"`)

              if (address) {
                try {
                  // Try to geocode the address
                  const coordinates = await geocodeAddress(address)

                  if (coordinates) {
                    const location: FraudsterLocation = {
                      id: node.id,
                      type: determineNodeType(node),
                      name: node.data.label || 'Unknown',
                      address: address,
                      accountNumber:
                        node.data.account_number ||
                        node.data.receiver_account ||
                        node.data.sender_account,
                      layer: node.data.layer || 0,
                      latitude: coordinates.lat,
                      longitude: coordinates.lng,
                      transactionCount: getTransactionCount(node),
                      totalAmount: getTotalAmount(node),
                      nodeData: node.data,
                      connections: []
                    }

                    allLocations.push(location)
                  }
                } catch (err) {
                  console.warn(`Failed to geocode address for node ${node.id}:`, err)
                }
              }
            }

            // Process edges to create connections
            if (complaint.graph_data.edges) {
              for (const edge of complaint.graph_data.edges) {
                const sourceLocation = allLocations.find((l) => l.id === edge.source)
                const targetLocation = allLocations.find((l) => l.id === edge.target)

                if (sourceLocation && targetLocation) {
                  sourceLocation.connections.push(targetLocation.id)

                  allConnections.push({
                    from: [sourceLocation.latitude, sourceLocation.longitude],
                    to: [targetLocation.latitude, targetLocation.longitude],
                    amount: parseFloat(edge.data?.amount || '0')
                  })
                }
              }
            }
          }
        }

        setFraudsterLocations(allLocations)
        setConnections(allConnections)
      } catch (err) {
        console.error('Error processing fraudster locations:', err)
        setError('Failed to load fraudster location data')
      } finally {
        setLoading(false)
      }
    }

    processFraudsterLocations()
  }, [complaints, selectedComplaint])

  // Enhanced address extraction from node data
  const extractAddressFromNode = (node: GraphNode): string | null => {
    const data = node.data

    // Try various address fields in order of preference
    const addressFields = [
      'address',
      'account_holder_address',
      'receiver_address',
      'sender_address',
      'branch_address',
      'location'
    ]

    // First, try to find a complete address
    for (const field of addressFields) {
      if (data[field] && typeof data[field] === 'string') {
        const address = data[field].trim()
        if (address.length > 10 && !address.toLowerCase().includes('not available')) {
          return address
        }
      }
    }

    // If no complete address, try to construct from components
    const addressComponents = []

    // Add account holder name if available
    if (data.account_holder && typeof data.account_holder === 'string') {
      addressComponents.push(data.account_holder.trim())
    }

    // Add city, district, state information
    const locationFields = ['city', 'district', 'state', 'branch_city', 'branch_state']
    for (const field of locationFields) {
      if (data[field] && typeof data[field] === 'string') {
        const location = data[field].trim()
        if (location.length > 2 && !addressComponents.includes(location)) {
          addressComponents.push(location)
        }
      }
    }

    // If we have enriched bank data, use that
    if (data.enriched_bank_data) {
      const bankData = data.enriched_bank_data
      if (bankData.branch_address && bankData.branch_address.length > 10) {
        return `${bankData.branch_address}, ${bankData.branch_city}, ${bankData.branch_state}`
      }
      if (bankData.branch_city && bankData.branch_state) {
        return `${bankData.branch_city}, ${bankData.branch_state}`
      }
    }

    // Return constructed address if we have enough components
    if (addressComponents.length >= 2) {
      return addressComponents.join(', ')
    }

    return null
  }

  // Determine node type based on layer and data
  const determineNodeType = (node: GraphNode): 'suspect' | 'victim' | 'intermediary' => {
    const layer = node.data.layer || 0

    if (layer === 0) {
      return 'victim' // Layer 0 is typically the victim
    } else if (layer === 1) {
      return 'suspect' // Layer 1 is typically direct fraudsters
    } else {
      return 'intermediary' // Higher layers are intermediaries
    }
  }

  // Get transaction count for a node
  const getTransactionCount = (node: GraphNode): number => {
    // This would need to be calculated based on the actual transaction data
    // For now, return a mock value
    return Math.floor(Math.random() * 10) + 1
  }

  // Get total amount for a node
  const getTotalAmount = (node: GraphNode): number => {
    const amount = node.data.amount || node.data.total_amount || '0'
    return parseFloat(amount.toString().replace(/[,]/g, '') || '0')
  }

  // Enhanced geocoding with better address parsing
  const geocodeAddress = async (address: string): Promise<{ lat: number; lng: number } | null> => {
    try {
      if (!address || address.length < 3) return null

      // Parse address components more intelligently
      const { cleanAddress, city, state } = parseAddressComponents(address)

      console.log(`Attempting to geocode: "${cleanAddress}", City: "${city}", State: "${state}"`)

      const result = await ifscEnrichmentService.geocodeAddress(cleanAddress, city, state)

      if (result) {
        console.log(`Successfully geocoded "${address}" to:`, result)
      } else {
        console.warn(`Failed to geocode address: "${address}"`)
      }

      return result
    } catch (error) {
      console.warn('Geocoding failed for address:', address, error)
      return null
    }
  }

  // Parse address components from various formats
  const parseAddressComponents = (
    address: string
  ): { cleanAddress: string; city: string; state: string } => {
    if (!address) return { cleanAddress: '', city: '', state: '' }

    // Clean the address
    const cleanAddress = address.trim()

    // Common Indian state names and abbreviations
    const indianStates = [
      'ANDHRA PRADESH',
      'ARUNACHAL PRADESH',
      'ASSAM',
      'BIHAR',
      'CHHATTISGARH',
      'DELHI',
      'GOA',
      'GUJARAT',
      'HARYANA',
      'HIMACHAL PRADESH',
      'JHARKHAND',
      'KARNATAKA',
      'KERALA',
      'MADHYA PRADESH',
      'MAHARASHTRA',
      'MANIPUR',
      'MEGHALAYA',
      'MIZORAM',
      'NAGALAND',
      'ODISHA',
      'PUNJAB',
      'RAJASTHAN',
      'SIKKIM',
      'TAMIL NADU',
      'TELANGANA',
      'TRIPURA',
      'UTTAR PRADESH',
      'UTTARAKHAND',
      'WEST BENGAL',
      'AP',
      'AR',
      'AS',
      'BR',
      'CG',
      'DL',
      'GA',
      'GJ',
      'HR',
      'HP',
      'JH',
      'KA',
      'KL',
      'MP',
      'MH',
      'MN',
      'ML',
      'MZ',
      'NL',
      'OR',
      'PB',
      'RJ',
      'SK',
      'TN',
      'TG',
      'TR',
      'UP',
      'UK',
      'WB'
    ]

    // Split by common delimiters
    const parts = cleanAddress
      .split(/[,\n\r\|;]/)
      .map((p) => p.trim())
      .filter((p) => p.length > 0)

    let city = ''
    let state = ''
    const addressParts = [...parts]

    // Try to identify state from the parts
    for (let i = parts.length - 1; i >= 0; i--) {
      const part = parts[i].toUpperCase()
      if (indianStates.some((s) => s === part || part.includes(s))) {
        state = parts[i]
        addressParts.splice(i, 1)
        break
      }
    }

    // Try to identify city (usually the last remaining part or second to last)
    if (addressParts.length > 0) {
      // If we found a state, city is likely the part before it
      if (state && parts.length > 1) {
        const stateIndex = parts.findIndex((p) => p.toUpperCase() === state.toUpperCase())
        if (stateIndex > 0) {
          city = parts[stateIndex - 1]
          const cityIndex = addressParts.findIndex((p) => p === city)
          if (cityIndex >= 0) {
            addressParts.splice(cityIndex, 1)
          }
        }
      } else {
        // Otherwise, assume the last part is the city
        city = addressParts.pop() || ''
      }
    }

    return {
      cleanAddress: addressParts.join(', '),
      city: city,
      state: state
    }
  }

  // Memoize marker creation for better performance
  const createCustomMarker = useCallback((location: FraudsterLocation) => {
    const colors = {
      victim: '#3b82f6', // Blue
      suspect: '#ef4444', // Red
      intermediary: '#f59e0b' // Amber
    }

    const icons = {
      victim: '👤', // Person
      suspect: '⚠️', // Warning
      intermediary: '🔄' // Cycle
    }

    const color = colors[location.type]
    const icon = icons[location.type]
    const size = Math.min(Math.max(location.transactionCount * 3 + 20, 25), 45)

    return L.divIcon({
      html: `<div style="
        background: linear-gradient(135deg, ${color}, ${color}dd);
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        border: 3px solid white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${Math.max(size / 3, 12)}px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        cursor: pointer;
      ">${icon}</div>`,
      className: `custom-fraudster-marker ${location.type}`,
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    })
  }, []) // Empty dependency array since marker creation logic is stable

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <div
        className={cn(
          'rounded-xl border p-8 min-h-[400px] flex items-center justify-center',
          isDark
            ? 'bg-white/5 border-white/10 text-white'
            : 'bg-white/80 border-gray-200/50 text-gray-900',
          className
        )}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div>Loading fraudster location data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div
        className={cn(
          'rounded-xl border p-8 min-h-[400px] flex items-center justify-center',
          isDark
            ? 'bg-white/5 border-white/10 text-white'
            : 'bg-white/80 border-gray-200/50 text-gray-900',
          className
        )}
      >
        <div className="text-center text-red-500">
          <div className="text-lg font-semibold mb-2">Error</div>
          <div>{error}</div>
        </div>
      </div>
    )
  }

  if (fraudsterLocations.length === 0) {
    return (
      <div
        className={cn(
          'rounded-xl border p-8 min-h-[400px] flex items-center justify-center',
          isDark
            ? 'bg-white/5 border-white/10 text-white'
            : 'bg-white/80 border-gray-200/50 text-gray-900',
          className
        )}
      >
        <div className="text-center">
          <div className="text-lg font-semibold mb-2">No Location Data</div>
          <div className="text-gray-500">
            No address information found in the selected complaints
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('rounded-xl border overflow-hidden', className)}>
      <div className="h-[500px] w-full">
        <MapContainer
          center={[20.5937, 78.9629]} // Center of India
          zoom={5}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          <TileLayer
            url={
              isDark
                ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
                : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
            }
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* Hotspot Analysis Overlay */}
          {showHotspots && (
            <HotspotVisualization
              complaints={complaints}
              selectedComplaint={selectedComplaint}
              analysisType="fraudster"
            />
          )}

          {/* Connection lines with enhanced styling */}
          {connections.map((connection, index) => {
            const weight = Math.max(Math.min(connection.amount / 25000, 8), 2)
            const opacity = Math.max(Math.min(connection.amount / 100000, 0.8), 0.3)

            return (
              <Polyline
                key={index}
                positions={[connection.from, connection.to]}
                color="#ef4444"
                weight={weight}
                opacity={opacity}
                dashArray={connection.amount > 100000 ? '0' : '8, 12'}
                className="transaction-flow"
                pathOptions={{
                  lineCap: 'round',
                  lineJoin: 'round'
                }}
              />
            )
          })}

          <MarkerClusterGroup
            chunkedLoading
            spiderfyOnMaxZoom={true}
            showCoverageOnHover={false}
            zoomToBoundsOnClick={true}
            maxClusterRadius={40}
          >
            {fraudsterLocations.map((location) => (
              <Marker
                key={location.id}
                position={[location.latitude, location.longitude]}
                icon={createCustomMarker(location)}
              >
                <Popup maxWidth={350}>
                  <div className="p-3 min-w-[300px]">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-bold text-lg text-gray-800">{location.name}</h3>
                      <span
                        className={cn(
                          'px-2 py-1 rounded-full text-xs font-semibold text-white',
                          location.type === 'victim' && 'bg-blue-500',
                          location.type === 'suspect' && 'bg-red-500',
                          location.type === 'intermediary' && 'bg-amber-500'
                        )}
                      >
                        {location.type.charAt(0).toUpperCase() + location.type.slice(1)}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <strong>Layer:</strong>
                        </div>
                        <div className="text-gray-600">{location.layer}</div>

                        {location.accountNumber && (
                          <>
                            <div>
                              <strong>Account:</strong>
                            </div>
                            <div className="font-mono text-blue-600">{location.accountNumber}</div>
                          </>
                        )}

                        <div>
                          <strong>Connections:</strong>
                        </div>
                        <div className="text-gray-600">{location.connections.length}</div>
                      </div>

                      <div className="pt-2 border-t border-gray-200">
                        <div className="text-gray-500 text-xs mb-1">Address:</div>
                        <div className="text-gray-600 text-xs">{location.address}</div>
                      </div>

                      <div className="pt-2 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center">
                            <div className="text-lg font-bold text-blue-600">
                              {location.transactionCount}
                            </div>
                            <div className="text-xs text-gray-500">Transactions</div>
                          </div>
                          <div className="text-center">
                            <div className="text-sm font-bold text-green-600">
                              {formatCurrency(location.totalAmount)}
                            </div>
                            <div className="text-xs text-gray-500">Amount</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MarkerClusterGroup>
        </MapContainer>
      </div>

      {/* Legend and Statistics */}
      <div
        className={cn(
          'p-4 border-t',
          isDark ? 'bg-white/5 border-white/10' : 'bg-white/80 border-gray-200/50'
        )}
      >
        <div className="space-y-4">
          {/* Entity Type Legend */}
          <div>
            <div className="text-sm font-semibold mb-2">Entity Types</div>
            <div className="flex flex-wrap items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                  👤
                </div>
                <span>
                  Victim (Layer 0) - {fraudsterLocations.filter((l) => l.type === 'victim').length}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-white text-xs">
                  ⚠️
                </div>
                <span>
                  Suspect (Layer 1) -{' '}
                  {fraudsterLocations.filter((l) => l.type === 'suspect').length}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center text-white text-xs">
                  🔄
                </div>
                <span>
                  Intermediary (Layer 2+) -{' '}
                  {fraudsterLocations.filter((l) => l.type === 'intermediary').length}
                </span>
              </div>
            </div>
          </div>

          {/* Connection Legend */}
          <div>
            <div className="text-sm font-semibold mb-2">Transaction Flows</div>
            <div className="flex flex-wrap items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-8 h-1 bg-red-500 opacity-80"></div>
                <span>Solid: High Amount (&gt; ₹1L)</span>
              </div>
              <div className="flex items-center gap-2">
                <div
                  className="w-8 h-1 bg-red-500 opacity-60"
                  style={{
                    backgroundImage:
                      'repeating-linear-gradient(to right, #ef4444 0, #ef4444 4px, transparent 4px, transparent 8px)'
                  }}
                ></div>
                <span>Dashed: Lower Amount (&lt; ₹1L)</span>
              </div>
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="flex items-center justify-between text-sm border-t pt-3">
            <div className="flex items-center gap-6">
              <div>
                <span className="font-semibold">{fraudsterLocations.length}</span>
                <span className="text-gray-500 ml-1">locations</span>
              </div>
              <div>
                <span className="font-semibold">{connections.length}</span>
                <span className="text-gray-500 ml-1">connections</span>
              </div>
              <div>
                <span className="font-semibold">
                  {fraudsterLocations.reduce((sum, l) => sum + l.transactionCount, 0)}
                </span>
                <span className="text-gray-500 ml-1">total transactions</span>
              </div>
            </div>
            <div className="text-gray-500 text-xs">Line thickness indicates transaction amount</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FraudsterLocationMap
