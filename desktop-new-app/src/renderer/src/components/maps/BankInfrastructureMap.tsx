import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import MarkerClusterGroup from 'react-leaflet-cluster'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { ComplaintData, TransactionData } from '../../../../shared/api'
import { ifscEnrichmentService } from '../../services/ifscEnrichmentService'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'
import HotspotVisualization from './HotspotVisualization'

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface BankLocation {
  id: string
  ifsc: string
  bankName: string
  branchName: string
  address: string
  city: string
  state: string
  district: string
  latitude: number
  longitude: number
  transactionCount: number
  totalAmount: number
  transactions: TransactionData[]
}

interface BankInfrastructureMapProps {
  complaints: ComplaintData[]
  selectedComplaint?: string | null
  className?: string
  showHotspots?: boolean
}

const BankInfrastructureMap: React.FC<BankInfrastructureMapProps> = ({
  complaints,
  selectedComplaint,
  className,
  showHotspots = true
}) => {
  const { isDark } = useThemeContext()
  const [bankLocations, setBankLocations] = useState<BankLocation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const mapRef = useRef<L.Map | null>(null)

  // Memoize complaints to process to avoid unnecessary re-processing
  const complaintsToProcess = useMemo(() => {
    return selectedComplaint
      ? complaints.filter(c => c.id === selectedComplaint)
      : complaints
  }, [complaints, selectedComplaint])

  // Memoize IFSC extraction to avoid re-computation
  const extractedIFSCs = useMemo(() => {
    const ifscGroups = new Map<string, TransactionData[]>()

    complaintsToProcess.forEach(complaint => {
      if (complaint.layer_transactions) {
        Object.values(complaint.layer_transactions).forEach(layerTransactions => {
          if (Array.isArray(layerTransactions)) {
            layerTransactions.forEach(transaction => {
              if (transaction.receiver_ifsc) {
                const ifsc = transaction.receiver_ifsc
                if (!ifscGroups.has(ifsc)) {
                  ifscGroups.set(ifsc, [])
                }
                ifscGroups.get(ifsc)!.push(transaction)
              }
            })
          }
        })
      }
    })

    return ifscGroups
  }, [complaintsToProcess])

  // Process complaints to extract bank locations
  useEffect(() => {
    const processBankLocations = async () => {
      try {
        setLoading(true)
        setError(null)

        // Use pre-computed IFSC groups for better performance
        const locations: BankLocation[] = []

        // Preload IFSC data for better performance
        const ifscCodes = Array.from(extractedIFSCs.keys())
        if (ifscCodes.length > 0) {
          await ifscEnrichmentService.preloadIFSCData(ifscCodes.slice(0, 20)) // Limit to first 20 for performance
        }

        for (const [ifsc, transactions] of extractedIFSCs.entries()) {
          try {
            // Validate and fetch IFSC details
            if (ifscEnrichmentService.validateIFSC(ifsc)) {
              console.log(`Processing IFSC: ${ifsc} with ${transactions.length} transactions`)
              const ifscDetails = await ifscEnrichmentService.fetchIFSCDetails(ifsc)

              if (ifscDetails) {
                console.log(`IFSC Details for ${ifsc}:`, {
                  BANK: ifscDetails.BANK,
                  BRANCH: ifscDetails.BRANCH,
                  ADDRESS: ifscDetails.ADDRESS,
                  CITY: ifscDetails.CITY,
                  STATE: ifscDetails.STATE,
                  DISTRICT: ifscDetails.DISTRICT
                })

                // Construct proper bank address for geocoding
                const bankAddress = constructBankAddress(ifscDetails)
                console.log(`Constructed address for ${ifsc}:`, bankAddress)

                // Try to get real geocoded coordinates
                let coordinates = await ifscEnrichmentService.geocodeAddress(
                  bankAddress.fullAddress,
                  bankAddress.city,
                  bankAddress.state
                )

                // Fallback to state/city coordinates if exact geocoding fails
                if (!coordinates) {
                  console.warn(`Geocoding failed for ${ifscDetails.BANK} ${ifscDetails.BRANCH}, using fallback coordinates`)
                  coordinates = generateFallbackCoordinates(ifscDetails.STATE, ifscDetails.CITY)
                } else {
                  console.log(`Successfully geocoded ${ifscDetails.BANK} ${ifscDetails.BRANCH} to:`, coordinates)
                }

                const totalAmount = transactions.reduce((sum, t) => {
                  const amount = parseFloat(t.amount?.replace(/[,]/g, '') || '0')
                  return sum + (isNaN(amount) ? 0 : amount)
                }, 0)

                locations.push({
                  id: ifsc,
                  ifsc: ifsc,
                  bankName: ifscDetails.BANK,
                  branchName: ifscDetails.BRANCH,
                  address: ifscDetails.ADDRESS,
                  city: ifscDetails.CITY,
                  state: ifscDetails.STATE,
                  district: ifscDetails.DISTRICT,
                  latitude: coordinates.lat,
                  longitude: coordinates.lng,
                  transactionCount: transactions.length,
                  totalAmount: totalAmount,
                  transactions: transactions
                })
              }
            }
          } catch (err) {
            console.warn(`Failed to process IFSC ${ifsc}:`, err)
          }
        }

        setBankLocations(locations)
      } catch (err) {
        console.error('Error processing bank locations:', err)
        setError('Failed to load bank location data')
      } finally {
        setLoading(false)
      }
    }

    processBankLocations()
  }, [extractedIFSCs]) // Only re-run when IFSC groups change

  // Construct proper bank address from IFSC details
  const constructBankAddress = (ifscDetails: any) => {
    const components = []

    // Add branch name if available and meaningful
    if (ifscDetails.BRANCH && ifscDetails.BRANCH.length > 3) {
      components.push(ifscDetails.BRANCH)
    }

    // Add bank name
    if (ifscDetails.BANK) {
      components.push(ifscDetails.BANK)
    }

    // Add address if available and meaningful
    if (ifscDetails.ADDRESS &&
        ifscDetails.ADDRESS.length > 3 &&
        !ifscDetails.ADDRESS.toLowerCase().includes('not available')) {
      components.push(ifscDetails.ADDRESS)
    }

    return {
      fullAddress: components.join(', '),
      city: ifscDetails.CITY || '',
      state: ifscDetails.STATE || ''
    }
  }

  // Generate fallback coordinates based on state/city when exact geocoding fails
  const generateFallbackCoordinates = (state: string, city: string) => {
    // Major Indian cities coordinates
    const cityCoordinates: Record<string, { lat: number; lng: number }> = {
      'MUMBAI': { lat: 19.0760, lng: 72.8777 },
      'DELHI': { lat: 28.6139, lng: 77.2090 },
      'BANGALORE': { lat: 12.9716, lng: 77.5946 },
      'BENGALURU': { lat: 12.9716, lng: 77.5946 },
      'HYDERABAD': { lat: 17.3850, lng: 78.4867 },
      'AHMEDABAD': { lat: 23.0225, lng: 72.5714 },
      'CHENNAI': { lat: 13.0827, lng: 80.2707 },
      'KOLKATA': { lat: 22.5726, lng: 88.3639 },
      'PUNE': { lat: 18.5204, lng: 73.8567 },
      'JAIPUR': { lat: 26.9124, lng: 75.7873 },
      'SURAT': { lat: 21.1702, lng: 72.8311 },
      'LUCKNOW': { lat: 26.8467, lng: 80.9462 },
      'KANPUR': { lat: 26.4499, lng: 80.3319 },
      'NAGPUR': { lat: 21.1458, lng: 79.0882 },
      'INDORE': { lat: 22.7196, lng: 75.8577 },
      'THANE': { lat: 19.2183, lng: 72.9781 },
      'BHOPAL': { lat: 23.2599, lng: 77.4126 },
      'VISAKHAPATNAM': { lat: 17.6868, lng: 83.2185 },
      'PIMPRI': { lat: 18.6298, lng: 73.7997 },
      'PATNA': { lat: 25.5941, lng: 85.1376 }
    }

    // State coordinates for fallback
    const stateCoordinates: Record<string, { lat: number; lng: number }> = {
      'ANDHRA PRADESH': { lat: 15.9129, lng: 79.7400 },
      'ARUNACHAL PRADESH': { lat: 28.2180, lng: 94.7278 },
      'ASSAM': { lat: 26.2006, lng: 92.9376 },
      'BIHAR': { lat: 25.0961, lng: 85.3131 },
      'CHHATTISGARH': { lat: 21.2787, lng: 81.8661 },
      'DELHI': { lat: 28.6139, lng: 77.2090 },
      'GOA': { lat: 15.2993, lng: 74.1240 },
      'GUJARAT': { lat: 23.0225, lng: 72.5714 },
      'HARYANA': { lat: 29.0588, lng: 76.0856 },
      'HIMACHAL PRADESH': { lat: 31.1048, lng: 77.1734 },
      'JHARKHAND': { lat: 23.6102, lng: 85.2799 },
      'KARNATAKA': { lat: 15.3173, lng: 75.7139 },
      'KERALA': { lat: 10.8505, lng: 76.2711 },
      'MADHYA PRADESH': { lat: 22.9734, lng: 78.6569 },
      'MAHARASHTRA': { lat: 19.7515, lng: 75.7139 },
      'MANIPUR': { lat: 24.6637, lng: 93.9063 },
      'MEGHALAYA': { lat: 25.4670, lng: 91.3662 },
      'MIZORAM': { lat: 23.1645, lng: 92.9376 },
      'NAGALAND': { lat: 26.1584, lng: 94.5624 },
      'ODISHA': { lat: 20.9517, lng: 85.0985 },
      'PUNJAB': { lat: 31.1471, lng: 75.3412 },
      'RAJASTHAN': { lat: 27.0238, lng: 74.2179 },
      'SIKKIM': { lat: 27.5330, lng: 88.5122 },
      'TAMIL NADU': { lat: 11.1271, lng: 78.6569 },
      'TELANGANA': { lat: 18.1124, lng: 79.0193 },
      'TRIPURA': { lat: 23.9408, lng: 91.9882 },
      'UTTAR PRADESH': { lat: 26.8467, lng: 80.9462 },
      'UTTARAKHAND': { lat: 30.0668, lng: 79.0193 },
      'WEST BENGAL': { lat: 22.9868, lng: 87.8550 }
    }

    // First try to match city
    const cityKey = city.toUpperCase()
    if (cityCoordinates[cityKey]) {
      return cityCoordinates[cityKey]
    }

    // Then try state
    const stateKey = state.toUpperCase()
    if (stateCoordinates[stateKey]) {
      const baseCoords = stateCoordinates[stateKey]
      // Add small random offset to avoid overlapping markers
      const offset = 0.1
      return {
        lat: baseCoords.lat + (Math.random() - 0.5) * offset,
        lng: baseCoords.lng + (Math.random() - 0.5) * offset
      }
    }

    // Default to India center
    return { lat: 20.5937, lng: 78.9629 }
  }

  // Memoize marker creation for better performance
  const createCustomMarker = useCallback((location: BankLocation) => {
    const baseSize = 25
    const sizeMultiplier = Math.min(Math.max(location.transactionCount / 2, 1), 3)
    const size = baseSize * sizeMultiplier

    // Enhanced color coding based on amount thresholds
    let color = '#22c55e' // Green for low amounts
    let riskLevel = 'Low'

    if (location.totalAmount > 500000) {
      color = '#dc2626' // Dark red for very high amounts
      riskLevel = 'Critical'
    } else if (location.totalAmount > 200000) {
      color = '#ef4444' // Red for high amounts
      riskLevel = 'High'
    } else if (location.totalAmount > 100000) {
      color = '#f97316' // Orange for medium-high amounts
      riskLevel = 'Medium-High'
    } else if (location.totalAmount > 50000) {
      color = '#eab308' // Yellow for medium amounts
      riskLevel = 'Medium'
    }

    const fontSize = Math.max(size / 4, 10)
    const displayText = location.transactionCount > 99 ? '99+' : location.transactionCount.toString()

    return L.divIcon({
      html: `<div style="
        background: linear-gradient(135deg, ${color}, ${color}dd);
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        border: 3px solid white;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: ${fontSize}px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 0 0 1px rgba(0,0,0,0.1);
        position: relative;
        cursor: pointer;
        transition: transform 0.2s ease;
      " title="Risk Level: ${riskLevel}">${displayText}</div>`,
      className: 'custom-bank-marker',
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2]
    })
  }, []) // Empty dependency array since marker creation logic is stable

  // Memoize risk statistics calculation
  const riskStats = useMemo(() => {
    const stats = {
      critical: 0,
      high: 0,
      mediumHigh: 0,
      medium: 0,
      low: 0,
      totalAmount: 0,
      totalTransactions: 0
    }

    bankLocations.forEach(location => {
      stats.totalAmount += location.totalAmount
      stats.totalTransactions += location.transactionCount

      if (location.totalAmount > 500000) {
        stats.critical++
      } else if (location.totalAmount > 200000) {
        stats.high++
      } else if (location.totalAmount > 100000) {
        stats.mediumHigh++
      } else if (location.totalAmount > 50000) {
        stats.medium++
      } else {
        stats.low++
      }
    })

    return stats
  }, [bankLocations]) // Recalculate only when bank locations change

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (loading) {
    return (
      <div className={cn(
        'rounded-xl border p-8 min-h-[400px] flex items-center justify-center',
        isDark
          ? 'bg-white/5 border-white/10 text-white'
          : 'bg-white/80 border-gray-200/50 text-gray-900',
        className
      )}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div>Loading bank infrastructure data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn(
        'rounded-xl border p-8 min-h-[400px] flex items-center justify-center',
        isDark
          ? 'bg-white/5 border-white/10 text-white'
          : 'bg-white/80 border-gray-200/50 text-gray-900',
        className
      )}>
        <div className="text-center text-red-500">
          <div className="text-lg font-semibold mb-2">Error</div>
          <div>{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('rounded-xl border overflow-hidden', className)}>
      <div className="h-[500px] w-full">
        <MapContainer
          center={[20.5937, 78.9629]} // Center of India
          zoom={5}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          <TileLayer
            url={isDark 
              ? "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"
              : "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            }
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* Hotspot Analysis Overlay */}
          {showHotspots && (
            <HotspotVisualization
              complaints={complaints}
              selectedComplaint={selectedComplaint}
              analysisType="bank"
            />
          )}

          <MarkerClusterGroup
            chunkedLoading
            spiderfyOnMaxZoom={true}
            showCoverageOnHover={false}
            zoomToBoundsOnClick={true}
            maxClusterRadius={50}
          >
            {bankLocations.map((location) => {
              const riskLevel = location.totalAmount > 500000 ? 'Critical' :
                              location.totalAmount > 200000 ? 'High' :
                              location.totalAmount > 100000 ? 'Medium-High' :
                              location.totalAmount > 50000 ? 'Medium' : 'Low'

              const riskColor = location.totalAmount > 500000 ? '#dc2626' :
                               location.totalAmount > 200000 ? '#ef4444' :
                               location.totalAmount > 100000 ? '#f97316' :
                               location.totalAmount > 50000 ? '#eab308' : '#22c55e'

              return (
                <Marker
                  key={location.id}
                  position={[location.latitude, location.longitude]}
                  icon={createCustomMarker(location)}
                >
                  <Popup maxWidth={350} className="bank-popup">
                    <div className="p-3 min-w-[300px]">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-lg text-gray-800">{location.bankName}</h3>
                        <span
                          className="px-2 py-1 rounded-full text-xs font-semibold text-white"
                          style={{ backgroundColor: riskColor }}
                        >
                          {riskLevel} Risk
                        </span>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="grid grid-cols-2 gap-2">
                          <div><strong>Branch:</strong></div>
                          <div className="text-gray-600">{location.branchName}</div>

                          <div><strong>IFSC:</strong></div>
                          <div className="font-mono text-blue-600">{location.ifsc}</div>

                          <div><strong>Location:</strong></div>
                          <div className="text-gray-600">{location.city}, {location.state}</div>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <div className="text-gray-500 text-xs mb-1">Address:</div>
                          <div className="text-gray-600 text-xs">{location.address}</div>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600">{location.transactionCount}</div>
                              <div className="text-xs text-gray-500">Transactions</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-green-600">{formatCurrency(location.totalAmount)}</div>
                              <div className="text-xs text-gray-500">Total Amount</div>
                            </div>
                          </div>
                        </div>

                        {location.transactions.length > 0 && (
                          <div className="pt-2 border-t border-gray-200">
                            <div className="text-xs text-gray-500 mb-1">Recent Transactions:</div>
                            <div className="max-h-20 overflow-y-auto">
                              {location.transactions.slice(0, 3).map((txn, idx) => (
                                <div key={idx} className="text-xs text-gray-600 py-1">
                                  {formatCurrency(parseFloat(txn.amount?.replace(/[,]/g, '') || '0'))} - {txn.date}
                                </div>
                              ))}
                              {location.transactions.length > 3 && (
                                <div className="text-xs text-blue-500">+{location.transactions.length - 3} more...</div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Popup>
                </Marker>
              )
            })}
          </MarkerClusterGroup>
        </MapContainer>
      </div>
      
      {/* Enhanced Legend and Statistics */}
      <div className={cn(
        'p-4 border-t',
        isDark ? 'bg-white/5 border-white/10' : 'bg-white/80 border-gray-200/50'
      )}>
        <div className="space-y-4">
          {/* Risk Level Legend */}
          <div>
            <div className="text-sm font-semibold mb-2">Risk Levels by Transaction Amount</div>
            <div className="flex flex-wrap items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Low (&lt; ₹50K) - {riskStats.low}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Medium (₹50K-₹1L) - {riskStats.medium}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                <span>Med-High (₹1L-₹2L) - {riskStats.mediumHigh}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span>High (₹2L-₹5L) - {riskStats.high}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-700"></div>
                <span>Critical (&gt; ₹5L) - {riskStats.critical}</span>
              </div>
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="flex items-center justify-between text-sm border-t pt-3">
            <div className="flex items-center gap-6">
              <div>
                <span className="font-semibold">{bankLocations.length}</span>
                <span className="text-gray-500 ml-1">bank branches</span>
              </div>
              <div>
                <span className="font-semibold">{riskStats.totalTransactions}</span>
                <span className="text-gray-500 ml-1">total transactions</span>
              </div>
              <div>
                <span className="font-semibold">{formatCurrency(riskStats.totalAmount)}</span>
                <span className="text-gray-500 ml-1">total amount</span>
              </div>
            </div>
            <div className="text-gray-500 text-xs">
              Marker size indicates transaction volume
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BankInfrastructureMap
