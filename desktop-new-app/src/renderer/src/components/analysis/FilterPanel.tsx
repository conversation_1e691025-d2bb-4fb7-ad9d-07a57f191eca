import React, { useState, useEffect } from 'react'
import { Filter, X, Calendar, DollarSign, MapPin, AlertTriangle, Search } from 'lucide-react'
import { Button } from '../ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'
import { ComplaintData, TransactionData } from '../../../../shared/api'

interface FilterCriteria {
  dateRange: {
    start: string
    end: string
  }
  amountRange: {
    min: number
    max: number
  }
  fraudTypes: string[]
  riskLevels: string[]
  geographicRegions: string[]
  banks: string[]
  layers: number[]
  transactionTypes: string[]
  searchQuery: string
}

interface FilterPanelProps {
  complaints: ComplaintData[]
  onFilterChange: (criteria: FilterCriteria, filteredData: any) => void
  className?: string
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  complaints,
  onFilterChange,
  className
}) => {
  const { isDark } = useThemeContext()
  const [isExpanded, setIsExpanded] = useState(false)
  const [filters, setFilters] = useState<FilterCriteria>({
    dateRange: { start: '', end: '' },
    amountRange: { min: 0, max: ******** },
    fraudTypes: [],
    riskLevels: [],
    geographicRegions: [],
    banks: [],
    layers: [],
    transactionTypes: [],
    searchQuery: ''
  })

  const [availableOptions, setAvailableOptions] = useState({
    fraudTypes: [] as string[],
    banks: [] as string[],
    states: [] as string[],
    transactionTypes: [] as string[],
    dateRange: { min: '', max: '' },
    amountRange: { min: 0, max: 0 }
  })

  // Extract available filter options from complaints data
  useEffect(() => {
    const extractOptions = () => {
      const fraudTypes = new Set<string>()
      const banks = new Set<string>()
      const states = new Set<string>()
      const transactionTypes = new Set<string>()
      const dates: Date[] = []
      const amounts: number[] = []

      complaints.forEach(complaint => {
        // Extract fraud types
        if (complaint.fraud_type) {
          fraudTypes.add(complaint.fraud_type)
        }

        // Extract from metadata
        if (complaint.metadata?.category) {
          fraudTypes.add(complaint.metadata.category)
        }

        // Extract from transactions
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions).forEach(layerTransactions => {
            if (Array.isArray(layerTransactions)) {
              layerTransactions.forEach(transaction => {
                // Banks
                if (transaction.receiver_bank) {
                  banks.add(transaction.receiver_bank)
                }
                if (transaction.sender_bank) {
                  banks.add(transaction.sender_bank)
                }

                // Transaction types
                if (transaction.txn_type) {
                  transactionTypes.add(transaction.txn_type)
                }
                if (transaction.type) {
                  transactionTypes.add(transaction.type)
                }

                // Dates
                if (transaction.date) {
                  const date = parseDate(transaction.date)
                  if (date) dates.push(date)
                }

                // Amounts
                const amount = parseFloat(transaction.amount?.replace(/[,]/g, '') || '0')
                if (amount > 0) amounts.push(amount)
              })
            }
          })
        }

        // Extract states from graph data
        if (complaint.graph_data?.nodes) {
          complaint.graph_data.nodes.forEach(node => {
            const state = node.data.state || node.data.branch_state
            if (state) states.add(state)
          })
        }
      })

      // Calculate date range
      const sortedDates = dates.sort((a, b) => a.getTime() - b.getTime())
      const minDate = sortedDates[0]?.toISOString().split('T')[0] || ''
      const maxDate = sortedDates[sortedDates.length - 1]?.toISOString().split('T')[0] || ''

      // Calculate amount range
      const minAmount = amounts.length > 0 ? Math.min(...amounts) : 0
      const maxAmount = amounts.length > 0 ? Math.max(...amounts) : ********

      setAvailableOptions({
        fraudTypes: Array.from(fraudTypes).sort(),
        banks: Array.from(banks).sort(),
        states: Array.from(states).sort(),
        transactionTypes: Array.from(transactionTypes).sort(),
        dateRange: { min: minDate, max: maxDate },
        amountRange: { min: minAmount, max: maxAmount }
      })

      // Initialize filters with available data
      setFilters(prev => ({
        ...prev,
        dateRange: { start: minDate, end: maxDate },
        amountRange: { min: minAmount, max: maxAmount }
      }))
    }

    extractOptions()
  }, [complaints])

  // Parse date string
  const parseDate = (dateStr: string): Date | null => {
    try {
      const formats = [
        /(\d{2})\/(\d{2})\/(\d{4})/,
        /(\d{4})-(\d{2})-(\d{2})/,
        /(\d{2})-(\d{2})-(\d{4})/,
      ]

      for (const format of formats) {
        const match = dateStr.match(format)
        if (match) {
          if (format === formats[1]) {
            return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]))
          } else {
            return new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]))
          }
        }
      }
      return new Date(dateStr)
    } catch {
      return null
    }
  }

  // Apply filters and notify parent
  useEffect(() => {
    const applyFilters = () => {
      const filteredComplaints = complaints.filter(complaint => {
        // Date filter
        if (filters.dateRange.start && filters.dateRange.end) {
          const complaintDate = complaint.metadata?.date ? parseDate(complaint.metadata.date) : null
          if (complaintDate) {
            const startDate = new Date(filters.dateRange.start)
            const endDate = new Date(filters.dateRange.end)
            if (complaintDate < startDate || complaintDate > endDate) {
              return false
            }
          }
        }

        // Fraud type filter
        if (filters.fraudTypes.length > 0) {
          const complaintFraudType = complaint.fraud_type || complaint.metadata?.category || ''
          if (!filters.fraudTypes.includes(complaintFraudType)) {
            return false
          }
        }

        // Search query filter
        if (filters.searchQuery) {
          const searchLower = filters.searchQuery.toLowerCase()
          const searchableText = [
            complaint.title,
            complaint.description,
            complaint.metadata?.complainant_name,
            complaint.metadata?.complaint_number
          ].join(' ').toLowerCase()
          
          if (!searchableText.includes(searchLower)) {
            return false
          }
        }

        return true
      })

      // Filter transactions within complaints
      const filteredData = {
        complaints: filteredComplaints,
        transactions: [] as TransactionData[],
        statistics: {
          totalComplaints: filteredComplaints.length,
          totalAmount: 0,
          totalTransactions: 0
        }
      }

      filteredComplaints.forEach(complaint => {
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions).forEach(layerTransactions => {
            if (Array.isArray(layerTransactions)) {
              layerTransactions.forEach(transaction => {
                // Amount filter
                const amount = parseFloat(transaction.amount?.replace(/[,]/g, '') || '0')
                if (amount < filters.amountRange.min || amount > filters.amountRange.max) {
                  return
                }

                // Bank filter
                if (filters.banks.length > 0) {
                  const transactionBanks = [transaction.receiver_bank, transaction.sender_bank].filter(Boolean)
                  if (!transactionBanks.some(bank => filters.banks.includes(bank))) {
                    return
                  }
                }

                // Transaction type filter
                if (filters.transactionTypes.length > 0) {
                  const txnType = transaction.txn_type || transaction.type || ''
                  if (!filters.transactionTypes.includes(txnType)) {
                    return
                  }
                }

                // Layer filter
                if (filters.layers.length > 0) {
                  if (!filters.layers.includes(transaction.layer)) {
                    return
                  }
                }

                filteredData.transactions.push(transaction)
                filteredData.statistics.totalAmount += amount
                filteredData.statistics.totalTransactions++
              })
            }
          })
        }
      })

      onFilterChange(filters, filteredData)
    }

    applyFilters()
  }, [filters, complaints, onFilterChange])

  // Handle filter changes
  const updateFilter = (key: keyof FilterCriteria, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // Toggle array filter values
  const toggleArrayFilter = (key: keyof FilterCriteria, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: (prev[key] as string[]).includes(value)
        ? (prev[key] as string[]).filter(item => item !== value)
        : [...(prev[key] as string[]), value]
    }))
  }

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      dateRange: { start: availableOptions.dateRange.min, end: availableOptions.dateRange.max },
      amountRange: { min: availableOptions.amountRange.min, max: availableOptions.amountRange.max },
      fraudTypes: [],
      riskLevels: [],
      geographicRegions: [],
      banks: [],
      layers: [],
      transactionTypes: [],
      searchQuery: ''
    })
  }

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0
    if (filters.fraudTypes.length > 0) count++
    if (filters.banks.length > 0) count++
    if (filters.transactionTypes.length > 0) count++
    if (filters.layers.length > 0) count++
    if (filters.searchQuery) count++
    if (filters.dateRange.start !== availableOptions.dateRange.min || 
        filters.dateRange.end !== availableOptions.dateRange.max) count++
    if (filters.amountRange.min !== availableOptions.amountRange.min || 
        filters.amountRange.max !== availableOptions.amountRange.max) count++
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Advanced Filters
            {activeFilterCount > 0 && (
              <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
                {activeFilterCount}
              </span>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Button size="sm" variant="outline" onClick={clearFilters}>
                <X className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium mb-2">
              <Search className="w-4 h-4 inline mr-1" />
              Search
            </label>
            <input
              type="text"
              value={filters.searchQuery}
              onChange={(e) => updateFilter('searchQuery', e.target.value)}
              placeholder="Search complaints, names, numbers..."
              className={cn(
                'w-full p-2 border rounded-lg',
                isDark ? 'bg-white/10 border-white/20 text-white' : 'bg-white border-gray-300'
              )}
            />
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Date Range
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="date"
                value={filters.dateRange.start}
                onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, start: e.target.value })}
                className={cn(
                  'p-2 border rounded-lg',
                  isDark ? 'bg-white/10 border-white/20 text-white' : 'bg-white border-gray-300'
                )}
              />
              <input
                type="date"
                value={filters.dateRange.end}
                onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, end: e.target.value })}
                className={cn(
                  'p-2 border rounded-lg',
                  isDark ? 'bg-white/10 border-white/20 text-white' : 'bg-white border-gray-300'
                )}
              />
            </div>
          </div>

          {/* Amount Range */}
          <div>
            <label className="block text-sm font-medium mb-2">
              <DollarSign className="w-4 h-4 inline mr-1" />
              Amount Range (₹)
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="number"
                value={filters.amountRange.min}
                onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, min: parseInt(e.target.value) || 0 })}
                placeholder="Min amount"
                className={cn(
                  'p-2 border rounded-lg',
                  isDark ? 'bg-white/10 border-white/20 text-white' : 'bg-white border-gray-300'
                )}
              />
              <input
                type="number"
                value={filters.amountRange.max}
                onChange={(e) => updateFilter('amountRange', { ...filters.amountRange, max: parseInt(e.target.value) || 0 })}
                placeholder="Max amount"
                className={cn(
                  'p-2 border rounded-lg',
                  isDark ? 'bg-white/10 border-white/20 text-white' : 'bg-white border-gray-300'
                )}
              />
            </div>
          </div>

          {/* Fraud Types */}
          {availableOptions.fraudTypes.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">
                <AlertTriangle className="w-4 h-4 inline mr-1" />
                Fraud Types
              </label>
              <div className="flex flex-wrap gap-2">
                {availableOptions.fraudTypes.map(type => (
                  <Button
                    key={type}
                    size="sm"
                    variant={filters.fraudTypes.includes(type) ? "default" : "outline"}
                    onClick={() => toggleArrayFilter('fraudTypes', type)}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Banks */}
          {availableOptions.banks.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">
                <MapPin className="w-4 h-4 inline mr-1" />
                Banks
              </label>
              <div className="max-h-32 overflow-y-auto">
                <div className="flex flex-wrap gap-2">
                  {availableOptions.banks.slice(0, 10).map(bank => (
                    <Button
                      key={bank}
                      size="sm"
                      variant={filters.banks.includes(bank) ? "default" : "outline"}
                      onClick={() => toggleArrayFilter('banks', bank)}
                    >
                      {bank}
                    </Button>
                  ))}
                  {availableOptions.banks.length > 10 && (
                    <span className="text-sm text-gray-500 self-center">
                      +{availableOptions.banks.length - 10} more...
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Transaction Types */}
          {availableOptions.transactionTypes.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">Transaction Types</label>
              <div className="flex flex-wrap gap-2">
                {availableOptions.transactionTypes.map(type => (
                  <Button
                    key={type}
                    size="sm"
                    variant={filters.transactionTypes.includes(type) ? "default" : "outline"}
                    onClick={() => toggleArrayFilter('transactionTypes', type)}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Layers */}
          <div>
            <label className="block text-sm font-medium mb-2">Transaction Layers</label>
            <div className="flex flex-wrap gap-2">
              {[0, 1, 2, 3, 4, 5].map(layer => (
                <Button
                  key={layer}
                  size="sm"
                  variant={filters.layers.includes(layer) ? "default" : "outline"}
                  onClick={() => toggleArrayFilter('layers', layer.toString())}
                >
                  Layer {layer}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}

export default FilterPanel
