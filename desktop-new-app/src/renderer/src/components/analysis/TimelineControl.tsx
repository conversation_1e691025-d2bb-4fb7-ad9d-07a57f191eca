import React, { useState, useEffect, useCallback } from 'react'
import { Play, Pause, SkipBack, Ski<PERSON><PERSON>or<PERSON>, Calendar, Clock, TrendingUp } from 'lucide-react'
import { Button } from '../ui/Button'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useThemeContext } from '../../context/useThemeContext'
import { cn } from '../../lib/aceternity-utils'
import { ComplaintData, TransactionData } from '../../../../shared/api'

interface TimelineEvent {
  date: Date
  transactions: TransactionData[]
  amount: number
  type: 'fraud_start' | 'peak_activity' | 'investigation' | 'resolution'
  description: string
}

interface TimelineControlProps {
  complaints: ComplaintData[]
  selectedComplaint?: string | null
  onTimeRangeChange?: (startDate: Date, endDate: Date) => void
  onAnimationFrame?: (currentDate: Date, events: TimelineEvent[]) => void
  className?: string
}

const TimelineControl: React.FC<TimelineControlProps> = ({
  complaints,
  selectedComplaint,
  onTimeRangeChange,
  onAnimationFrame,
  className
}) => {
  const { isDark } = useThemeContext()
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([])
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0) // 0-100 percentage
  const [playbackSpeed, setPlaybackSpeed] = useState(1) // 1x, 2x, 4x
  const [dateRange, setDateRange] = useState<{ start: Date; end: Date } | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState<
    'all' | '7d' | '30d' | '90d' | 'custom'
  >('all')

  // Extract timeline events from complaints
  useEffect(() => {
    const extractTimelineEvents = () => {
      const events: TimelineEvent[] = []
      const complaintsToProcess = selectedComplaint
        ? complaints.filter((c) => c.id === selectedComplaint)
        : complaints

      complaintsToProcess.forEach((complaint) => {
        // Extract events from complaint metadata
        if (complaint.metadata?.date) {
          const complaintDate = parseDate(complaint.metadata.date)
          if (complaintDate) {
            events.push({
              date: complaintDate,
              transactions: [],
              amount: parseFloat(complaint.metadata.total_amount?.replace(/[,]/g, '') || '0'),
              type: 'fraud_start',
              description: `Complaint filed: ${complaint.title}`
            })
          }
        }

        // Extract events from transactions
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions).forEach((layerTransactions) => {
            if (Array.isArray(layerTransactions)) {
              layerTransactions.forEach((transaction) => {
                const transactionDate = parseDate(transaction.date)
                if (transactionDate) {
                  const amount = parseFloat(transaction.amount?.replace(/[,]/g, '') || '0')

                  // Find existing event for this date or create new one
                  const existingEvent = events.find(
                    (e) =>
                      e.date.toDateString() === transactionDate.toDateString() &&
                      e.type === 'peak_activity'
                  )

                  if (existingEvent) {
                    existingEvent.transactions.push(transaction)
                    existingEvent.amount += amount
                  } else {
                    events.push({
                      date: transactionDate,
                      transactions: [transaction],
                      amount: amount,
                      type: 'peak_activity',
                      description: `Transaction activity: ${formatCurrency(amount)}`
                    })
                  }
                }
              })
            }
          })
        }
      })

      // Sort events by date
      events.sort((a, b) => a.date.getTime() - b.date.getTime())

      // Set date range
      if (events.length > 0) {
        const start = events[0].date
        const end = events[events.length - 1].date
        setDateRange({ start, end })
      }

      setTimelineEvents(events)
    }

    extractTimelineEvents()
  }, [complaints, selectedComplaint])

  // Parse date string in various formats
  const parseDate = (dateStr: string): Date | null => {
    try {
      // Try different date formats
      const formats = [
        /(\d{2})\/(\d{2})\/(\d{4})/, // DD/MM/YYYY
        /(\d{4})-(\d{2})-(\d{2})/, // YYYY-MM-DD
        /(\d{2})-(\d{2})-(\d{4})/ // DD-MM-YYYY
      ]

      for (const format of formats) {
        const match = dateStr.match(format)
        if (match) {
          if (format === formats[1]) {
            // YYYY-MM-DD
            return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]))
          } else {
            // DD/MM/YYYY or DD-MM-YYYY
            return new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]))
          }
        }
      }

      // Fallback to Date constructor
      return new Date(dateStr)
    } catch {
      return null
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Animation loop
  useEffect(() => {
    let animationFrame: number

    if (isPlaying && dateRange) {
      const animate = () => {
        setCurrentTime((prev) => {
          const newTime = prev + playbackSpeed * 0.5 // Adjust speed
          if (newTime >= 100) {
            setIsPlaying(false)
            return 100
          }

          // Calculate current date based on timeline position
          const totalDuration = dateRange.end.getTime() - dateRange.start.getTime()
          const currentTimestamp = dateRange.start.getTime() + (totalDuration * newTime) / 100
          const currentDate = new Date(currentTimestamp)

          // Get events up to current time
          const currentEvents = timelineEvents.filter((event) => event.date <= currentDate)
          onAnimationFrame?.(currentDate, currentEvents)

          return newTime
        })

        animationFrame = requestAnimationFrame(animate)
      }

      animationFrame = requestAnimationFrame(animate)
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [isPlaying, playbackSpeed, dateRange, timelineEvents, onAnimationFrame])

  // Handle time range selection
  const handleTimeRangeChange = (range: 'all' | '7d' | '30d' | '90d' | 'custom') => {
    setSelectedTimeRange(range)

    if (!dateRange) return

    let startDate = dateRange.start
    let endDate = dateRange.end

    const now = new Date()
    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'all':
      default:
        startDate = dateRange.start
        endDate = dateRange.end
        break
    }

    onTimeRangeChange?.(startDate, endDate)
  }

  // Get current date based on timeline position
  const getCurrentDate = () => {
    if (!dateRange) return new Date()
    const totalDuration = dateRange.end.getTime() - dateRange.start.getTime()
    const currentTimestamp = dateRange.start.getTime() + (totalDuration * currentTime) / 100
    return new Date(currentTimestamp)
  }

  // Get events statistics
  const getEventStats = () => {
    const totalAmount = timelineEvents.reduce((sum, event) => sum + event.amount, 0)
    const peakDay = timelineEvents.reduce(
      (max, event) => (event.amount > max.amount ? event : max),
      timelineEvents[0] || { amount: 0, date: new Date() }
    )

    return {
      totalEvents: timelineEvents.length,
      totalAmount,
      peakDay: peakDay.date,
      peakAmount: peakDay.amount
    }
  }

  const stats = getEventStats()

  if (!dateRange || timelineEvents.length === 0) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <Clock className="w-8 h-8 mx-auto mb-2" />
            <div>No timeline data available</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Temporal Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Timeline Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={() => setCurrentTime(0)}>
              <SkipBack className="w-4 h-4" />
            </Button>
            <Button size="sm" onClick={() => setIsPlaying(!isPlaying)}>
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button size="sm" variant="outline" onClick={() => setCurrentTime(100)}>
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          {/* Speed Control */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Speed:</span>
            {[1, 2, 4].map((speed) => (
              <Button
                key={speed}
                size="sm"
                variant={playbackSpeed === speed ? 'default' : 'outline'}
                onClick={() => setPlaybackSpeed(speed)}
              >
                {speed}x
              </Button>
            ))}
          </div>
        </div>

        {/* Timeline Scrubber */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500">
            <span>{dateRange.start.toLocaleDateString()}</span>
            <span>{getCurrentDate().toLocaleDateString()}</span>
            <span>{dateRange.end.toLocaleDateString()}</span>
          </div>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="100"
              value={currentTime}
              onChange={(e) => setCurrentTime(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            {/* Event markers */}
            <div className="absolute top-0 left-0 w-full h-2 pointer-events-none">
              {timelineEvents.map((event, idx) => {
                const position =
                  ((event.date.getTime() - dateRange.start.getTime()) /
                    (dateRange.end.getTime() - dateRange.start.getTime())) *
                  100
                return (
                  <div
                    key={idx}
                    className={cn(
                      'absolute w-1 h-2 rounded-full',
                      event.type === 'fraud_start' && 'bg-red-500',
                      event.type === 'peak_activity' && 'bg-orange-500',
                      event.type === 'investigation' && 'bg-blue-500',
                      event.type === 'resolution' && 'bg-green-500'
                    )}
                    style={{ left: `${position}%` }}
                    title={event.description}
                  />
                )
              })}
            </div>
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-500">Range:</span>
          {['all', '7d', '30d', '90d'].map((range) => (
            <Button
              key={range}
              size="sm"
              variant={selectedTimeRange === range ? 'default' : 'outline'}
              onClick={() => handleTimeRangeChange(range as any)}
            >
              {range === 'all' ? 'All' : range.toUpperCase()}
            </Button>
          ))}
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{stats.totalEvents}</div>
            <div className="text-xs text-gray-500">Events</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {formatCurrency(stats.totalAmount)}
            </div>
            <div className="text-xs text-gray-500">Total Amount</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600">
              {stats.peakDay.toLocaleDateString()}
            </div>
            <div className="text-xs text-gray-500">Peak Day</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">{formatCurrency(stats.peakAmount)}</div>
            <div className="text-xs text-gray-500">Peak Amount</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default TimelineControl
