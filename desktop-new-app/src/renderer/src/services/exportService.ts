// Export and Reporting Service for Advanced Analysis
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { ComplaintData, TransactionData } from '../../../shared/api'
import { Hotspot } from './hotspotAnalysisService'

interface ExportOptions {
  format: 'pdf' | 'csv' | 'json' | 'image'
  includeCharts: boolean
  includeHotspots: boolean
  includeTimeline: boolean
  dateRange?: { start: Date; end: Date }
}

interface AnalysisReport {
  metadata: {
    generatedAt: string
    analysisType: string
    dateRange: { start: string; end: string }
    totalComplaints: number
    totalTransactions: number
    totalAmount: number
  }
  summary: {
    keyFindings: string[]
    riskAssessment: string
    recommendations: string[]
  }
  hotspots: Hotspot[]
  transactions: TransactionData[]
  complaints: ComplaintData[]
}

class ExportService {
  /**
   * Export map visualization as image
   */
  async exportMapAsImage(
    mapElementId: string,
    filename: string = 'fraud-analysis-map'
  ): Promise<void> {
    try {
      const mapElement = document.getElementById(mapElementId)
      if (!mapElement) {
        throw new Error('Map element not found')
      }

      const canvas = await html2canvas(mapElement, {
        useCORS: true,
        allowTaint: true,
        scale: 2, // Higher quality
        backgroundColor: '#ffffff'
      })

      // Download as PNG
      const link = document.createElement('a')
      link.download = `${filename}.png`
      link.href = canvas.toDataURL('image/png')
      link.click()
    } catch (error) {
      console.error('Error exporting map as image:', error)
      throw new Error('Failed to export map as image')
    }
  }

  /**
   * Generate comprehensive analysis report
   */
  generateAnalysisReport(
    complaints: ComplaintData[],
    filteredData: any,
    hotspots: Hotspot[] = [],
    analysisType: string = 'comprehensive'
  ): AnalysisReport {
    const now = new Date()
    const transactions = filteredData?.transactions || []

    // Calculate date range
    const dates = transactions
      .map((t: TransactionData) => this.parseDate(t.date))
      .filter(Boolean)
      .sort((a: Date, b: Date) => a.getTime() - b.getTime())

    const dateRange = {
      start: dates[0]?.toISOString() || now.toISOString(),
      end: dates[dates.length - 1]?.toISOString() || now.toISOString()
    }

    // Calculate totals
    const totalAmount = transactions.reduce((sum: number, t: TransactionData) => {
      const amount = parseFloat(t.amount?.replace(/[,]/g, '') || '0')
      return sum + amount
    }, 0)

    // Generate key findings
    const keyFindings = this.generateKeyFindings(complaints, transactions, hotspots)

    // Generate risk assessment
    const riskAssessment = this.generateRiskAssessment(hotspots, totalAmount, transactions.length)

    // Generate recommendations
    const recommendations = this.generateRecommendations(hotspots, keyFindings)

    return {
      metadata: {
        generatedAt: now.toISOString(),
        analysisType,
        dateRange,
        totalComplaints: complaints.length,
        totalTransactions: transactions.length,
        totalAmount
      },
      summary: {
        keyFindings,
        riskAssessment,
        recommendations
      },
      hotspots,
      transactions,
      complaints
    }
  }

  /**
   * Export analysis report as PDF
   */
  async exportReportAsPDF(
    report: AnalysisReport,
    filename: string = 'fraud-analysis-report'
  ): Promise<void> {
    try {
      const pdf = new jsPDF()
      let yPosition = 20

      // Title
      pdf.setFontSize(20)
      pdf.text('Fraud Analysis Report', 20, yPosition)
      yPosition += 20

      // Metadata
      pdf.setFontSize(12)
      pdf.text(
        `Generated: ${new Date(report.metadata.generatedAt).toLocaleString()}`,
        20,
        yPosition
      )
      yPosition += 10
      pdf.text(`Analysis Type: ${report.metadata.analysisType}`, 20, yPosition)
      yPosition += 10
      pdf.text(
        `Date Range: ${new Date(report.metadata.dateRange.start).toLocaleDateString()} - ${new Date(report.metadata.dateRange.end).toLocaleDateString()}`,
        20,
        yPosition
      )
      yPosition += 20

      // Summary Statistics
      pdf.setFontSize(16)
      pdf.text('Summary Statistics', 20, yPosition)
      yPosition += 15

      pdf.setFontSize(12)
      pdf.text(`Total Complaints: ${report.metadata.totalComplaints}`, 20, yPosition)
      yPosition += 10
      pdf.text(`Total Transactions: ${report.metadata.totalTransactions}`, 20, yPosition)
      yPosition += 10
      pdf.text(`Total Amount: ${this.formatCurrency(report.metadata.totalAmount)}`, 20, yPosition)
      yPosition += 20

      // Key Findings
      if (report.summary.keyFindings.length > 0) {
        pdf.setFontSize(16)
        pdf.text('Key Findings', 20, yPosition)
        yPosition += 15

        pdf.setFontSize(12)
        report.summary.keyFindings.forEach((finding, index) => {
          const lines = pdf.splitTextToSize(`${index + 1}. ${finding}`, 170)
          pdf.text(lines, 20, yPosition)
          yPosition += lines.length * 7
        })
        yPosition += 10
      }

      // Risk Assessment
      if (report.summary.riskAssessment) {
        pdf.setFontSize(16)
        pdf.text('Risk Assessment', 20, yPosition)
        yPosition += 15

        pdf.setFontSize(12)
        const riskLines = pdf.splitTextToSize(report.summary.riskAssessment, 170)
        pdf.text(riskLines, 20, yPosition)
        yPosition += riskLines.length * 7 + 10
      }

      // Recommendations
      if (report.summary.recommendations.length > 0) {
        pdf.setFontSize(16)
        pdf.text('Recommendations', 20, yPosition)
        yPosition += 15

        pdf.setFontSize(12)
        report.summary.recommendations.forEach((rec, index) => {
          const lines = pdf.splitTextToSize(`${index + 1}. ${rec}`, 170)
          pdf.text(lines, 20, yPosition)
          yPosition += lines.length * 7
        })
      }

      // Hotspots Summary (if available)
      if (report.hotspots.length > 0) {
        pdf.addPage()
        yPosition = 20

        pdf.setFontSize(16)
        pdf.text('Fraud Hotspots Analysis', 20, yPosition)
        yPosition += 20

        report.hotspots.slice(0, 5).forEach((hotspot, index) => {
          pdf.setFontSize(14)
          pdf.text(`Hotspot ${index + 1} - ${hotspot.riskLevel.toUpperCase()} Risk`, 20, yPosition)
          yPosition += 15

          pdf.setFontSize(12)
          pdf.text(`Transactions: ${hotspot.transactionCount}`, 20, yPosition)
          yPosition += 7
          pdf.text(`Total Amount: ${this.formatCurrency(hotspot.totalAmount)}`, 20, yPosition)
          yPosition += 7
          pdf.text(`Intensity: ${(hotspot.intensity * 100).toFixed(1)}%`, 20, yPosition)
          yPosition += 7
          pdf.text(`Radius: ${hotspot.radius.toFixed(1)} km`, 20, yPosition)
          yPosition += 15

          if (yPosition > 250) {
            pdf.addPage()
            yPosition = 20
          }
        })
      }

      // Save PDF
      pdf.save(`${filename}.pdf`)
    } catch (error) {
      console.error('Error exporting report as PDF:', error)
      throw new Error('Failed to export report as PDF')
    }
  }

  /**
   * Export data as CSV
   */
  exportDataAsCSV(data: TransactionData[], filename: string = 'fraud-analysis-data'): void {
    try {
      if (data.length === 0) {
        throw new Error('No data to export')
      }

      // Get all unique keys from the data
      const allKeys = new Set<string>()
      data.forEach((item) => {
        Object.keys(item).forEach((key) => allKeys.add(key))
      })

      const headers = Array.from(allKeys)

      // Create CSV content
      const csvContent = [
        headers.join(','), // Header row
        ...data.map((item) =>
          headers
            .map((header) => {
              const value = item[header as keyof TransactionData]
              // Escape commas and quotes in values
              const stringValue = String(value || '')
              return stringValue.includes(',') || stringValue.includes('"')
                ? `"${stringValue.replace(/"/g, '""')}"`
                : stringValue
            })
            .join(',')
        )
      ].join('\n')

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${filename}.csv`
      link.click()
    } catch (error) {
      console.error('Error exporting data as CSV:', error)
      throw new Error('Failed to export data as CSV')
    }
  }

  /**
   * Export data as JSON
   */
  exportDataAsJSON(data: any, filename: string = 'fraud-analysis-data'): void {
    try {
      const jsonContent = JSON.stringify(data, null, 2)
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${filename}.json`
      link.click()
    } catch (error) {
      console.error('Error exporting data as JSON:', error)
      throw new Error('Failed to export data as JSON')
    }
  }

  /**
   * Generate evidence package with multiple formats
   */
  async generateEvidencePackage(
    report: AnalysisReport,
    mapElementId?: string,
    packageName: string = 'fraud-evidence-package'
  ): Promise<void> {
    try {
      // Create a zip-like structure by downloading multiple files
      const timestamp = new Date().toISOString().split('T')[0]
      const baseFilename = `${packageName}-${timestamp}`

      // Export report as PDF
      await this.exportReportAsPDF(report, `${baseFilename}-report`)

      // Export transaction data as CSV
      if (report.transactions.length > 0) {
        this.exportDataAsCSV(report.transactions, `${baseFilename}-transactions`)
      }

      // Export full data as JSON
      this.exportDataAsJSON(report, `${baseFilename}-complete-data`)

      // Export map image if element provided
      if (mapElementId) {
        await this.exportMapAsImage(mapElementId, `${baseFilename}-map`)
      }

      console.log('Evidence package generated successfully')
    } catch (error) {
      console.error('Error generating evidence package:', error)
      throw new Error('Failed to generate evidence package')
    }
  }

  // Helper methods
  private parseDate(dateStr: string): Date | null {
    try {
      const formats = [
        /(\d{2})\/(\d{2})\/(\d{4})/,
        /(\d{4})-(\d{2})-(\d{2})/,
        /(\d{2})-(\d{2})-(\d{4})/
      ]

      for (const format of formats) {
        const match = dateStr.match(format)
        if (match) {
          if (format === formats[1]) {
            return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]))
          } else {
            return new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]))
          }
        }
      }
      return new Date(dateStr)
    } catch {
      return null
    }
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount)
  }

  private generateKeyFindings(
    complaints: ComplaintData[],
    transactions: TransactionData[],
    hotspots: Hotspot[]
  ): string[] {
    const findings: string[] = []

    // Transaction volume analysis
    if (transactions.length > 100) {
      findings.push(
        `High transaction volume detected: ${transactions.length} transactions analyzed`
      )
    }

    // Amount analysis
    const totalAmount = transactions.reduce((sum, t) => {
      return sum + parseFloat(t.amount?.replace(/[,]/g, '') || '0')
    }, 0)

    if (totalAmount > ********) {
      findings.push(
        `Significant financial impact: Total amount exceeds ${this.formatCurrency(********)}`
      )
    }

    // Hotspot analysis
    const criticalHotspots = hotspots.filter((h) => h.riskLevel === 'critical')
    if (criticalHotspots.length > 0) {
      findings.push(
        `${criticalHotspots.length} critical fraud hotspots identified requiring immediate attention`
      )
    }

    // Bank diversity
    const uniqueBanks = new Set(transactions.map((t) => t.receiver_bank).filter(Boolean))
    if (uniqueBanks.size > 10) {
      findings.push(`Wide banking network involved: ${uniqueBanks.size} different banks affected`)
    }

    // Fraud type analysis
    const fraudTypes = new Set(complaints.map((c) => c.fraud_type).filter(Boolean))
    if (fraudTypes.size > 1) {
      findings.push(`Multiple fraud types detected: ${Array.from(fraudTypes).join(', ')}`)
    }

    return findings
  }

  private generateRiskAssessment(
    hotspots: Hotspot[],
    totalAmount: number,
    transactionCount: number
  ): string {
    const criticalHotspots = hotspots.filter((h) => h.riskLevel === 'critical').length
    const highRiskHotspots = hotspots.filter((h) => h.riskLevel === 'high').length

    if (criticalHotspots > 0 || totalAmount > ********) {
      return 'CRITICAL RISK: Immediate law enforcement intervention required. Evidence suggests organized fraud network with significant financial impact.'
    } else if (highRiskHotspots > 2 || totalAmount > ********) {
      return 'HIGH RISK: Coordinated investigation recommended. Pattern indicates sophisticated fraud operation requiring multi-jurisdictional response.'
    } else if (transactionCount > 50 || totalAmount > 1000000) {
      return 'MEDIUM RISK: Enhanced monitoring and investigation warranted. Fraud activity shows concerning patterns requiring attention.'
    } else {
      return 'LOW RISK: Standard investigation procedures sufficient. Limited scope fraud activity with contained impact.'
    }
  }

  private generateRecommendations(hotspots: Hotspot[], keyFindings: string[]): string[] {
    const recommendations: string[] = []

    if (hotspots.some((h) => h.riskLevel === 'critical')) {
      recommendations.push('Deploy specialized fraud investigation team to critical hotspot areas')
      recommendations.push('Coordinate with banking authorities for immediate account monitoring')
    }

    if (keyFindings.some((f) => f.includes('banking network'))) {
      recommendations.push('Establish inter-bank communication protocol for real-time fraud alerts')
    }

    if (hotspots.length > 3) {
      recommendations.push('Consider establishing regional task forces for coordinated response')
    }

    recommendations.push('Implement enhanced transaction monitoring in identified high-risk areas')
    recommendations.push('Share intelligence with relevant law enforcement agencies')
    recommendations.push('Consider public awareness campaigns in affected regions')

    return recommendations
  }
}

// Export singleton instance
export const exportService = new ExportService()
export type { ExportOptions, AnalysisReport }
