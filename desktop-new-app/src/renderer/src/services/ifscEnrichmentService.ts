// Browser-compatible IFSC service - removed Node.js dependencies

// Define the IFSC details interface based on the ifsc package
interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

// Enhanced bank data structure
interface EnrichedBankData {
  original_bank_name: string
  bank_name: string
  branch_name: string
  branch_address: string
  branch_state: string
  branch_city: string
  branch_district: string
  ifsc_code: string
  is_ifsc_valid: boolean
  enrichment_status: 'success' | 'invalid_ifsc' | 'api_error' | 'no_ifsc'
  enriched_at: string
  // Geographic coordinates
  latitude?: number
  longitude?: number
  geocoding_status?: 'success' | 'failed' | 'not_attempted'
}

// Transaction data interface (matching the backend structure)
interface TransactionData {
  receiver_ifsc?: string
  receiver_bank?: string
  [key: string]: any
}

class IFSCEnrichmentService {
  private cache: Map<string, IFSCDetails | null> = new Map()
  private geocodeCache: Map<string, { lat: number; lng: number } | null> = new Map()
  private readonly maxCacheSize = 1000
  private readonly cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Validate an IFSC code (browser-compatible)
   */
  validateIFSC(ifscCode: string): boolean {
    try {
      if (!ifscCode || typeof ifscCode !== 'string') {
        return false
      }

      // Clean the IFSC code
      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Basic format validation (4 letters + 7 characters)
      if (!/^[A-Z]{4}[A-Z0-9]{7}$/.test(cleanIFSC)) {
        return false
      }

      // Additional validation rules for common IFSC patterns
      // First 4 characters should be valid bank code letters
      const bankCode = cleanIFSC.substring(0, 4)
      if (!/^[A-Z]{4}$/.test(bankCode)) {
        return false
      }

      // 5th character should be 0 (reserved for future use)
      if (cleanIFSC.charAt(4) !== '0') {
        return false
      }

      // Last 6 characters should be alphanumeric
      const branchCode = cleanIFSC.substring(5)
      if (!/^[A-Z0-9]{6}$/.test(branchCode)) {
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating IFSC:', error)
      return false
    }
  }

  /**
   * Fetch IFSC details with caching (using IPC to main process)
   */
  async fetchIFSCDetails(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      if (!ifscCode) return null

      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Check cache first
      if (this.cache.has(cleanIFSC)) {
        return this.cache.get(cleanIFSC) || null
      }

      // Validate before fetching
      if (!this.validateIFSC(cleanIFSC)) {
        this.cache.set(cleanIFSC, null)
        return null
      }

      // Use IPC to fetch from main process (which can use Node.js packages)
      const details = await window.api.ifsc.fetchDetails(cleanIFSC)

      // Cache the result
      this.cacheResult(cleanIFSC, details)

      return details
    } catch (error) {
      console.error(`Error fetching IFSC details for ${ifscCode}:`, error)
      // Cache null result to avoid repeated API calls for invalid codes
      this.cacheResult(ifscCode.trim().toUpperCase(), null)
      return null
    }
  }

  /**
   * Cache management
   */
  private cacheResult(ifscCode: string, details: IFSCDetails | null): void {
    // Simple LRU cache implementation
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(ifscCode, details)
  }

  /**
   * Geocode an address to get latitude and longitude
   * Enhanced for Indian address formats with multiple fallback strategies
   */
  async geocodeAddress(address: string, city: string, state: string): Promise<{ lat: number; lng: number } | null> {
    try {
      // Clean and normalize the input data
      const cleanAddress = this.cleanAddressString(address)
      const cleanCity = this.cleanAddressString(city)
      const cleanState = this.normalizeStateName(state)

      // Create multiple address variations for better geocoding success
      const addressVariations = this.createAddressVariations(cleanAddress, cleanCity, cleanState)

      // Try each address variation
      for (const addressVariation of addressVariations) {
        const cacheKey = addressVariation.toLowerCase().trim()

        // Check cache first
        if (this.geocodeCache.has(cacheKey)) {
          const cached = this.geocodeCache.get(cacheKey)
          if (cached) return cached
        }

        // Try geocoding with this variation
        const coordinates = await this.tryNominatimGeocoding(addressVariation)

        if (coordinates) {
          // Cache the successful result for all variations
          addressVariations.forEach(variation => {
            this.cacheGeocodeResult(variation.toLowerCase().trim(), coordinates)
          })
          return coordinates
        }

        // Add small delay between requests to respect rate limits
        await this.delay(200)
      }

      // If all variations fail, try city/state level coordinates
      const fallbackCoordinates = this.getStateCoordinates(cleanState, cleanCity)

      // Cache the fallback result
      addressVariations.forEach(variation => {
        this.cacheGeocodeResult(variation.toLowerCase().trim(), fallbackCoordinates)
      })

      return fallbackCoordinates
    } catch (error) {
      console.error('Error geocoding address:', error)
      return null
    }
  }

  /**
   * Clean and normalize address strings for better geocoding
   */
  private cleanAddressString(address: string): string {
    if (!address) return ''

    return address
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[,]{2,}/g, ',') // Replace multiple commas with single comma
      .replace(/^,+|,+$/g, '') // Remove leading/trailing commas
      .replace(/\b(PIN|PINCODE|PIN CODE)\s*:?\s*\d{6}\b/gi, '') // Remove PIN codes
      .replace(/\b(PHONE|PH|TEL|MOBILE)\s*:?\s*[\d\s\-\+\(\)]+/gi, '') // Remove phone numbers
      .replace(/\b(EMAIL|E-MAIL)\s*:?\s*[\w\.\-]+@[\w\.\-]+/gi, '') // Remove emails
      .trim()
  }

  /**
   * Normalize state names to standard format
   */
  private normalizeStateName(state: string): string {
    if (!state) return ''

    const stateMapping: Record<string, string> = {
      'AP': 'ANDHRA PRADESH',
      'AR': 'ARUNACHAL PRADESH',
      'AS': 'ASSAM',
      'BR': 'BIHAR',
      'CG': 'CHHATTISGARH',
      'DL': 'DELHI',
      'GA': 'GOA',
      'GJ': 'GUJARAT',
      'HR': 'HARYANA',
      'HP': 'HIMACHAL PRADESH',
      'JH': 'JHARKHAND',
      'KA': 'KARNATAKA',
      'KL': 'KERALA',
      'MP': 'MADHYA PRADESH',
      'MH': 'MAHARASHTRA',
      'MN': 'MANIPUR',
      'ML': 'MEGHALAYA',
      'MZ': 'MIZORAM',
      'NL': 'NAGALAND',
      'OR': 'ODISHA',
      'PB': 'PUNJAB',
      'RJ': 'RAJASTHAN',
      'SK': 'SIKKIM',
      'TN': 'TAMIL NADU',
      'TG': 'TELANGANA',
      'TR': 'TRIPURA',
      'UP': 'UTTAR PRADESH',
      'UK': 'UTTARAKHAND',
      'WB': 'WEST BENGAL'
    }

    const normalized = state.toUpperCase().trim()
    return stateMapping[normalized] || normalized
  }

  /**
   * Create multiple address variations for better geocoding success
   */
  private createAddressVariations(address: string, city: string, state: string): string[] {
    const variations: string[] = []

    if (!address && !city && !state) return variations

    // Variation 1: Full address with all components
    if (address && city && state) {
      variations.push(`${address}, ${city}, ${state}, India`)
    }

    // Variation 2: Address without redundant city if city is already in address
    if (address && city && state && !address.toLowerCase().includes(city.toLowerCase())) {
      variations.push(`${address}, ${city}, ${state}, India`)
    }

    // Variation 3: City and state only (for bank branches)
    if (city && state) {
      variations.push(`${city}, ${state}, India`)
    }

    // Variation 4: State only as last resort
    if (state) {
      variations.push(`${state}, India`)
    }

    // Variation 5: Address and state (skip city if redundant)
    if (address && state) {
      variations.push(`${address}, ${state}, India`)
    }

    // Remove duplicates and empty variations
    return [...new Set(variations.filter(v => v.trim().length > 0))]
  }

  /**
   * Add delay for rate limiting
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Enhanced Nominatim geocoding with better error handling and retry logic
   */
  private async tryNominatimGeocoding(address: string): Promise<{ lat: number; lng: number } | null> {
    try {
      const encodedAddress = encodeURIComponent(address)
      const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=3&countrycodes=in&addressdetails=1`

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'NCRP-Matrix-Desktop-App/1.0',
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Geocoding failed: ${response.status}`)
      }

      const data = await response.json()

      if (data && data.length > 0) {
        // Prefer results with higher importance or better address match
        const bestResult = data.find((result: any) =>
          result.importance > 0.5 ||
          result.class === 'place' ||
          result.type === 'city' ||
          result.type === 'town'
        ) || data[0]

        const coordinates = {
          lat: parseFloat(bestResult.lat),
          lng: parseFloat(bestResult.lon)
        }

        // Validate coordinates are within India bounds
        if (this.isValidIndianCoordinate(coordinates.lat, coordinates.lng)) {
          console.log(`Geocoded "${address}" to:`, coordinates)
          return coordinates
        }
      }

      return null
    } catch (error) {
      console.warn('Nominatim geocoding failed for:', address, error)
      return null
    }
  }

  /**
   * Validate if coordinates are within Indian geographical bounds
   */
  private isValidIndianCoordinate(lat: number, lng: number): boolean {
    // India's approximate geographical bounds
    const INDIA_BOUNDS = {
      north: 37.6,
      south: 6.4,
      east: 97.25,
      west: 68.7
    }

    return lat >= INDIA_BOUNDS.south &&
           lat <= INDIA_BOUNDS.north &&
           lng >= INDIA_BOUNDS.west &&
           lng <= INDIA_BOUNDS.east
  }

  /**
   * Get approximate coordinates for Indian states and major cities
   */
  private getStateCoordinates(state: string, city: string): { lat: number; lng: number } | null {
    const stateCoordinates: Record<string, { lat: number; lng: number }> = {
      'ANDHRA PRADESH': { lat: 15.9129, lng: 79.7400 },
      'ARUNACHAL PRADESH': { lat: 28.2180, lng: 94.7278 },
      'ASSAM': { lat: 26.2006, lng: 92.9376 },
      'BIHAR': { lat: 25.0961, lng: 85.3131 },
      'CHHATTISGARH': { lat: 21.2787, lng: 81.8661 },
      'DELHI': { lat: 28.6139, lng: 77.2090 },
      'GOA': { lat: 15.2993, lng: 74.1240 },
      'GUJARAT': { lat: 23.0225, lng: 72.5714 },
      'HARYANA': { lat: 29.0588, lng: 76.0856 },
      'HIMACHAL PRADESH': { lat: 31.1048, lng: 77.1734 },
      'JHARKHAND': { lat: 23.6102, lng: 85.2799 },
      'KARNATAKA': { lat: 15.3173, lng: 75.7139 },
      'KERALA': { lat: 10.8505, lng: 76.2711 },
      'MADHYA PRADESH': { lat: 22.9734, lng: 78.6569 },
      'MAHARASHTRA': { lat: 19.7515, lng: 75.7139 },
      'MANIPUR': { lat: 24.6637, lng: 93.9063 },
      'MEGHALAYA': { lat: 25.4670, lng: 91.3662 },
      'MIZORAM': { lat: 23.1645, lng: 92.9376 },
      'NAGALAND': { lat: 26.1584, lng: 94.5624 },
      'ODISHA': { lat: 20.9517, lng: 85.0985 },
      'PUNJAB': { lat: 31.1471, lng: 75.3412 },
      'RAJASTHAN': { lat: 27.0238, lng: 74.2179 },
      'SIKKIM': { lat: 27.5330, lng: 88.5122 },
      'TAMIL NADU': { lat: 11.1271, lng: 78.6569 },
      'TELANGANA': { lat: 18.1124, lng: 79.0193 },
      'TRIPURA': { lat: 23.9408, lng: 91.9882 },
      'UTTAR PRADESH': { lat: 26.8467, lng: 80.9462 },
      'UTTARAKHAND': { lat: 30.0668, lng: 79.0193 },
      'WEST BENGAL': { lat: 22.9868, lng: 87.8550 }
    }

    const normalizedState = state.toUpperCase().trim()
    const baseCoords = stateCoordinates[normalizedState]

    if (!baseCoords) {
      // Default to India center if state not found
      return { lat: 20.5937, lng: 78.9629 }
    }

    // Add small random offset for different cities within the state
    const offset = 0.3
    return {
      lat: baseCoords.lat + (Math.random() - 0.5) * offset,
      lng: baseCoords.lng + (Math.random() - 0.5) * offset
    }
  }

  /**
   * Construct proper bank address from IFSC details
   */
  private constructBankAddress(ifscDetails: IFSCDetails): { fullAddress: string; city: string; state: string } {
    const components = []

    // Add branch name if available and different from bank name
    if (ifscDetails.BRANCH && !ifscDetails.BRANCH.toLowerCase().includes('branch')) {
      components.push(ifscDetails.BRANCH)
    }

    // Add bank name
    if (ifscDetails.BANK) {
      components.push(ifscDetails.BANK)
    }

    // Add address if available and meaningful
    if (ifscDetails.ADDRESS &&
        ifscDetails.ADDRESS.length > 3 &&
        !ifscDetails.ADDRESS.toLowerCase().includes('not available')) {
      components.push(ifscDetails.ADDRESS)
    }

    // Add district if available and different from city
    if (ifscDetails.DISTRICT &&
        ifscDetails.DISTRICT !== ifscDetails.CITY &&
        !ifscDetails.DISTRICT.toLowerCase().includes('not available')) {
      components.push(ifscDetails.DISTRICT)
    }

    return {
      fullAddress: components.join(', '),
      city: ifscDetails.CITY || '',
      state: ifscDetails.STATE || ''
    }
  }

  /**
   * Cache geocoding results
   */
  private cacheGeocodeResult(address: string, coordinates: { lat: number; lng: number } | null): void {
    if (this.geocodeCache.size >= this.maxCacheSize) {
      const firstKey = this.geocodeCache.keys().next().value
      this.geocodeCache.delete(firstKey)
    }

    this.geocodeCache.set(address, coordinates)
  }

  /**
   * Enrich bank data for a single transaction
   */
  async enrichTransactionBankData(transaction: TransactionData): Promise<TransactionData> {
    try {
      const receiverIFSC = transaction.receiver_ifsc
      const originalBankName = transaction.receiver_bank || ''
      
      // Create enriched bank data object
      const enrichedBankData: EnrichedBankData = {
        original_bank_name: originalBankName,
        bank_name: originalBankName,
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: receiverIFSC || '',
        is_ifsc_valid: false,
        enrichment_status: 'no_ifsc',
        enriched_at: new Date().toISOString()
      }
      
      if (!receiverIFSC) {
        // No IFSC code available
        enrichedBankData.enrichment_status = 'no_ifsc'
      } else {
        // Validate IFSC
        const isValid = this.validateIFSC(receiverIFSC)
        enrichedBankData.is_ifsc_valid = isValid
        
        if (!isValid) {
          enrichedBankData.enrichment_status = 'invalid_ifsc'
        } else {
          // Fetch IFSC details
          const ifscDetails = await this.fetchIFSCDetails(receiverIFSC)
          
          if (ifscDetails) {
            // Update with enriched data
            enrichedBankData.bank_name = ifscDetails.BANK || originalBankName
            enrichedBankData.branch_name = ifscDetails.BRANCH || ''
            enrichedBankData.branch_address = ifscDetails.ADDRESS || ''
            enrichedBankData.branch_state = ifscDetails.STATE || ''
            enrichedBankData.branch_city = ifscDetails.CITY || ''
            enrichedBankData.branch_district = ifscDetails.DISTRICT || ''
            enrichedBankData.enrichment_status = 'success'

            // Try to geocode the address using enhanced bank address construction
            try {
              const bankAddress = this.constructBankAddress(ifscDetails)
              const coordinates = await this.geocodeAddress(
                bankAddress.fullAddress,
                bankAddress.city,
                bankAddress.state
              )

              if (coordinates) {
                enrichedBankData.latitude = coordinates.lat
                enrichedBankData.longitude = coordinates.lng
                enrichedBankData.geocoding_status = 'success'
                console.log(`Successfully geocoded bank: ${ifscDetails.BANK} ${ifscDetails.BRANCH} to`, coordinates)
              } else {
                enrichedBankData.geocoding_status = 'failed'
                console.warn(`Failed to geocode bank address for IFSC: ${receiverIFSC}`)
              }
            } catch (error) {
              console.warn('Geocoding failed for IFSC:', receiverIFSC, error)
              enrichedBankData.geocoding_status = 'failed'
            }

            // Update transaction with standardized bank information
            transaction.receiver_bank = `${ifscDetails.BANK} - ${ifscDetails.BRANCH}`
            transaction.receiver_bank_full = ifscDetails.BANK
            transaction.receiver_branch = ifscDetails.BRANCH
            transaction.receiver_district = ifscDetails.DISTRICT
            transaction.receiver_state = ifscDetails.STATE
          } else {
            enrichedBankData.enrichment_status = 'api_error'
            enrichedBankData.geocoding_status = 'not_attempted'
          }
        }
      }
      
      // Add enriched bank data to transaction
      transaction.enriched_bank_data = enrichedBankData
      
      return transaction
    } catch (error) {
      console.error('Error enriching transaction bank data:', error)
      
      // Add error information to transaction
      transaction.enriched_bank_data = {
        original_bank_name: transaction.receiver_bank || '',
        bank_name: transaction.receiver_bank || '',
        branch_name: '',
        branch_address: '',
        branch_state: '',
        branch_city: '',
        branch_district: '',
        ifsc_code: transaction.receiver_ifsc || '',
        is_ifsc_valid: false,
        enrichment_status: 'api_error',
        enriched_at: new Date().toISOString()
      }
      
      return transaction
    }
  }

  /**
   * Enrich bank data for multiple transactions
   */
  async enrichTransactionsBankData(transactions: TransactionData[]): Promise<TransactionData[]> {
    const enrichedTransactions: TransactionData[] = []

    // Process transactions in batches to avoid overwhelming the API
    const batchSize = 10
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize)

      // Process batch concurrently
      const enrichedBatch = await Promise.all(
        batch.map(transaction => this.enrichTransactionBankData(transaction))
      )

      enrichedTransactions.push(...enrichedBatch)

      // Add small delay between batches to be respectful to the API
      if (i + batchSize < transactions.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    return enrichedTransactions
  }

  /**
   * Enrich complaint data with IFSC information
   * Updates bank names across layer_transactions, bank_notice_data, and graph_data
   */
  async enrichComplaintData(complaintData: any): Promise<any> {
    try {
      const enrichedData = { ...complaintData }

      // Enrich layer_transactions
      if (enrichedData.layer_transactions) {
        const enrichedLayerTransactions: any = {}

        for (const [layer, transactions] of Object.entries(enrichedData.layer_transactions)) {
          if (Array.isArray(transactions)) {
            enrichedLayerTransactions[layer] = await this.enrichTransactionsBankData(transactions)
          } else {
            enrichedLayerTransactions[layer] = transactions
          }
        }

        enrichedData.layer_transactions = enrichedLayerTransactions
      }

      // Enrich graph_data nodes
      if (enrichedData.graph_data?.nodes) {
        const enrichedNodes = await Promise.all(
          enrichedData.graph_data.nodes.map(async (node: any) => {
            if (node.data?.receiver_ifsc && node.data?.receiver_bank) {
              const ifscDetails = await this.fetchIFSCDetails(node.data.receiver_ifsc)
              if (ifscDetails) {
                return {
                  ...node,
                  data: {
                    ...node.data,
                    receiver_bank: ifscDetails.BANK,
                    bank: ifscDetails.BANK,
                    enriched_bank_data: {
                      original_bank_name: node.data.receiver_bank,
                      bank_name: ifscDetails.BANK,
                      branch_name: ifscDetails.BRANCH || '',
                      branch_address: ifscDetails.ADDRESS || '',
                      branch_state: ifscDetails.STATE || '',
                      branch_city: ifscDetails.CITY || '',
                      branch_district: ifscDetails.DISTRICT || '',
                      ifsc_code: node.data.receiver_ifsc,
                      is_ifsc_valid: true,
                      enrichment_status: 'success',
                      enriched_at: new Date().toISOString()
                    }
                  }
                }
              }
            }
            return node
          })
        )

        enrichedData.graph_data.nodes = enrichedNodes
      }

      // Enrich bank_notice_data
      if (enrichedData.bank_notice_data?.transactions) {
        enrichedData.bank_notice_data.transactions = await this.enrichTransactionsBankData(
          enrichedData.bank_notice_data.transactions
        )
      }

      return enrichedData
    } catch (error) {
      console.error('Error enriching complaint data:', error)
      return complaintData // Return original data if enrichment fails
    }
  }

  /**
   * Get enrichment statistics
   */
  getEnrichmentStats(transactions: TransactionData[]): {
    total: number
    enriched: number
    invalid_ifsc: number
    no_ifsc: number
    api_errors: number
  } {
    const stats = {
      total: transactions.length,
      enriched: 0,
      invalid_ifsc: 0,
      no_ifsc: 0,
      api_errors: 0
    }
    
    transactions.forEach(transaction => {
      const enrichedData = transaction.enriched_bank_data as EnrichedBankData
      if (enrichedData) {
        switch (enrichedData.enrichment_status) {
          case 'success':
            stats.enriched++
            break
          case 'invalid_ifsc':
            stats.invalid_ifsc++
            break
          case 'no_ifsc':
            stats.no_ifsc++
            break
          case 'api_error':
            stats.api_errors++
            break
        }
      }
    })
    
    return stats
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
    this.geocodeCache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { ifscCacheSize: number; geocodeCacheSize: number } {
    return {
      ifscCacheSize: this.cache.size,
      geocodeCacheSize: this.geocodeCache.size
    }
  }

  /**
   * Preload IFSC data for better performance
   */
  async preloadIFSCData(ifscCodes: string[]): Promise<void> {
    const batchSize = 5
    for (let i = 0; i < ifscCodes.length; i += batchSize) {
      const batch = ifscCodes.slice(i, i + batchSize)
      await Promise.all(batch.map(ifsc => this.fetchIFSCDetails(ifsc)))

      // Small delay to avoid overwhelming the API
      if (i + batchSize < ifscCodes.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
  }
}

// Export singleton instance
export const ifscEnrichmentService = new IFSCEnrichmentService()
export type { EnrichedBankData, TransactionData }
