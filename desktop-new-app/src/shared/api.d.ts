export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResponse {
  success: boolean
  token?: string
  error?: string
}

export interface SignupCredentials {
  email: string
  password: string
  initial: string
  name: string
  designation: string
  police_station: string
  district: string
  state: string
}

export interface SignupResponse {
  success: boolean
  token?: string
  error?: string
  message?: string
  requires_verification?: boolean
  email?: string
}

export interface VerifyEmailResponse {
  success: boolean
  message?: string
  error?: string
}

export interface ResendOtpResponse {
  success: boolean
  message?: string
  error?: string
}

export interface User {
  id: string
  email: string
  initial?: string
  name?: string
  designation?: string
  police_station?: string
  district?: string
  state?: string
  is_active: boolean
  email_verified: boolean
  roles: string[]
  created_at: string // ISO 8601 string
  last_login?: string // ISO 8601 string
}

export interface NodeData {
  label: string
  layer?: number
  [key: string]: unknown // Allow arbitrary properties
}

export interface EdgeData {
  label?: string
  [key: string]: unknown // Allow arbitrary properties
}

export interface GraphNode {
  id: string
  position: { x: number; y: number }
  data: NodeData
  type?: string
}

export interface GraphEdge {
  id: string
  source: string
  target: string
  type?: string
  data?: EdgeData
}

export interface ComplaintGraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
  transactions: TransactionData[] // Specific type based on provided structure
  metadata: Record<string, unknown> // Specific type based on provided structure
  max_layer: number
}

export interface ComplaintData {
  id: string
  title: string
  description: string
  file_name?: string
  fraud_type?: string
  extraction_type?: string
  max_layer?: number
  metadata?: Record<string, unknown> | null // Complaint metadata from HTML
  transactions?: TransactionData[] | null // All extracted transactions
  layer_transactions?: Record<string, unknown> | null // Layer-organized transactions
  bank_notice_data?: Record<string, unknown> | null // Bank notice generation data
  graph_data?: ComplaintGraphData | null // Graph visualization data
  extraction_info?: Record<string, unknown> | null // Extraction metadata
  status?: string
  created_at: string
  updated_at: string
}

export interface TransactionData {
  fraud_type: string
  sender_account: string
  sender_transaction_id: string
  sender_bank: string
  layer: number
  txn_type: string
  type: string
  date: string
  receiver_bank: string
  receiver_account: string
  receiver_transaction_id: string
  amount: string
  receiver_ifsc: string
  receiver_info: string
  reference: string
  sr_no: string
  extracted_at: string
  source: string
  is_valid: string
  validation_errors: string
  // Add optional properties for txn_id and transaction_id
  txn_id?: string
  transaction_id?: string
}

// IFSC Details interface
export interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

export interface API {
  auth: {
    login: (credentials: LoginCredentials) => Promise<LoginResponse>
    signup: (credentials: SignupCredentials) => Promise<SignupResponse>
    logout: () => Promise<void>
    getSession: () => Promise<User | null>
    hasToken: () => Promise<boolean>
    setToken: (token: string) => Promise<{ success: boolean; error?: string }>
    getToken: () => Promise<{ token: string | null; error?: string }>
    clearToken: () => Promise<{ success: boolean; error?: string }>
    verifyEmail: (data: { email: string; otp: string }) => Promise<VerifyEmailResponse>
    resendOtp: (data: { email: string }) => Promise<ResendOtpResponse>
  }
  database: {
    storeComplaint: (
      complaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>
    ) => Promise<string>
    getComplaints: () => Promise<ComplaintData[]>
    getComplaint: (id: string) => Promise<ComplaintData | null>
    deleteComplaint: (id: string) => Promise<boolean>
    updateComplaint: (
      id: string,
      data: Partial<Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>>
    ) => Promise<boolean>
  }
  backupDatabase: () => Promise<{ success: boolean; data?: string; error?: string }>
  deleteComplaintsByMonth: (month: string) => Promise<{ success: boolean; error?: string }>
  uploadHtml: (data: {
    fileContent: string
    fileName: string
    fraudType: string
    layerDepth: number
    skipThresholdAmount: number
  }) => Promise<{
    success: boolean
    complaintId?: string
    error?: string
    message?: string
  }>
  ifsc: {
    validate: (ifscCode: string) => Promise<boolean>
    fetchDetails: (ifscCode: string) => Promise<IFSCDetails | null>
  }
  template: {
    store: (templateData: {
      name: string
      content: string
      fileName: string
    }) => Promise<{ success: boolean; error?: string }>
    get: () => Promise<{
      success: boolean
      template?: { name: string; content: string; fileName: string }
      error?: string
    }>
    delete: () => Promise<{ success: boolean; error?: string }>
  }
}

export interface IpcRendererApi {
  getComplaintGraphData: (complaintId: string) => Promise<ComplaintGraphData>
  updateGraphNode: (complaintId: string, nodeId: string, data: NodeData) => Promise<void>
  addGraphNode: (complaintId: string, node: GraphNode) => Promise<void>
  deleteGraphNode: (complaintId: string, nodeId: string) => Promise<void>
  addGraphEdge: (complaintId: string, edge: GraphEdge) => Promise<void>
  deleteGraphEdge: (complaintId: string, edgeId: string) => Promise<void>
}

// Extend the Electron API with our custom IPC methods
declare module '@electron-toolkit/preload' {
  interface ElectronApi {
    ipcRenderer: IpcRendererApi
  }
}
