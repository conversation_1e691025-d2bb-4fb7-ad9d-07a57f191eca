// eslint-disable-next-line @typescript-eslint/no-require-imports
const { validate } = require('ifsc') as { validate: (ifscCode: string) => boolean }

// IFSC Details interface
interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

// Define interfaces for API responses to avoid using 'any'
interface RazorpayResponse {
  BANK?: string
  BRANCH?: string
  ADDRESS?: string
  CONTACT?: string
  CITY?: string
  RTGS?: boolean | string
  NEFT?: boolean | string
  IMPS?: boolean | string
  UPI?: boolean | string
  DISTRICT?: string
  STATE?: string
  BANKCODE?: string
  MICR?: string
}

interface JustinClicksResponse {
  BANK?: string
  BRANCH?: string
  ADDRESS?: string
  CONTACT?: string
  CITY?: string
  RTGS?: 'Y' | 'N' | boolean
  NEFT?: 'Y' | 'N' | boolean
  IMPS?: 'Y' | 'N' | boolean
  UPI?: 'Y' | 'N' | boolean
  DISTRICT?: string
  STATE?: string
  BANKCODE?: string
  MICR?: string
}

/**
 * Main process IFSC service using Node.js packages
 */
export class MainIFSCService {
  private cache: Map<string, IFSCDetails | null> = new Map()
  private readonly maxCacheSize = 1000
  private readonly cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours
  private cacheTimestamps: Map<string, number> = new Map()

  /**
   * Validate an IFSC code using the ifsc package
   */
  public validateIFSC(ifscCode: string): boolean {
    try {
      if (!ifscCode || typeof ifscCode !== 'string') {
        return false
      }

      // Clean the IFSC code
      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Basic format validation (4 letters + 7 characters)
      if (!/^[A-Z]{4}[A-Z0-9]{7}$/.test(cleanIFSC)) {
        return false
      }

      // Use the ifsc package validation (Node.js only)
      return validate(cleanIFSC)
    } catch (error) {
      console.error('[Main IFSC Service] Error validating IFSC:', error)
      return false
    }
  }

  /**
   * Fetch IFSC details using public API
   */
  public async fetchIFSCDetails(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      if (!ifscCode) return null

      const cleanIFSC = ifscCode.trim().toUpperCase()

      // Check cache first
      if (this.cache.has(cleanIFSC)) {
        const timestamp = this.cacheTimestamps.get(cleanIFSC)
        if (timestamp && Date.now() - timestamp < this.cacheExpiryMs) {
          console.log(`[Main IFSC Service] Cache hit for ${cleanIFSC}`)
          return this.cache.get(cleanIFSC) || null
        } else {
          // Cache expired, remove it
          this.cache.delete(cleanIFSC)
          this.cacheTimestamps.delete(cleanIFSC)
        }
      }

      // Validate before fetching
      if (!this.validateIFSC(cleanIFSC)) {
        console.log(`[Main IFSC Service] Invalid IFSC format: ${cleanIFSC}`)
        this.cacheResult(cleanIFSC, null)
        return null
      }

      console.log(`[Main IFSC Service] Fetching details for ${cleanIFSC}`)

      // Fetch from public IFSC API
      const details = await this.fetchFromAPI(cleanIFSC)

      // Cache the result
      this.cacheResult(cleanIFSC, details)

      if (details) {
        console.log(`[Main IFSC Service] Successfully fetched details for ${cleanIFSC}`)
      } else {
        console.log(`[Main IFSC Service] No details found for ${cleanIFSC}`)
      }
      return details
    } catch (error) {
      console.error(`[Main IFSC Service] Error fetching IFSC details for ${ifscCode}:`, error)
      // Cache null result to avoid repeated API calls for invalid codes
      this.cacheResult(ifscCode.trim().toUpperCase(), null)
      return null
    }
  }

  /**
   * Fetch IFSC details from public API
   */
  private async fetchFromAPI(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      // Try multiple APIs for better reliability
      const apis = [
        `https://ifsc.razorpay.com/${ifscCode}`,
        `https://bank-apis.justinclicks.com/API/V1/IFSC/${ifscCode}/`
      ]

      for (const apiUrl of apis) {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10-second timeout

        try {
          const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'User-Agent': 'IFSC-Lookup-Service/1.0'
            },
            signal: controller.signal // Abort signal for timeout
          })

          clearTimeout(timeoutId)

          if (response.ok) {
            const data = (await response.json()) as RazorpayResponse | JustinClicksResponse

            // Handle different API response formats
            if (apiUrl.includes('razorpay.com')) {
              return this.normalizeRazorpayResponse(data as RazorpayResponse, ifscCode)
            } else if (apiUrl.includes('justinclicks.com')) {
              return this.normalizeJustinClicksResponse(data as JustinClicksResponse, ifscCode)
            }
          }
        } catch (apiError: unknown) {
          clearTimeout(timeoutId)
          if (apiError instanceof Error && apiError.name === 'AbortError') {
            console.log(`[Main IFSC Service] API ${apiUrl} timed out.`)
          } else {
            console.log(`[Main IFSC Service] API ${apiUrl} failed:`, apiError)
          }
          continue // Try next API
        }
      }

      return null // No API succeeded
    } catch (error) {
      console.error('[Main IFSC Service] Error in fetchFromAPI:', error)
      return null
    }
  }

  /**
   * Normalize Razorpay API response
   */
  private normalizeRazorpayResponse(data: RazorpayResponse, ifscCode: string): IFSCDetails | null {
    try {
      if (!data || typeof data !== 'object') return null

      return {
        BANK: data.BANK || 'Unknown',
        IFSC: ifscCode,
        BRANCH: data.BRANCH || 'Unknown',
        ADDRESS: data.ADDRESS || 'Unknown',
        CONTACT: data.CONTACT || '',
        CITY: data.CITY || 'Unknown',
        RTGS: data.RTGS === 'true' || data.RTGS === true,
        NEFT: data.NEFT === 'true' || data.NEFT === true,
        IMPS: data.IMPS === 'true' || data.IMPS === true,
        UPI: data.UPI === 'true' || data.UPI === true,
        DISTRICT: data.DISTRICT || 'Unknown',
        STATE: data.STATE || 'Unknown',
        BANKCODE: data.BANKCODE || ifscCode.substring(0, 4),
        MICR: data.MICR || undefined
      }
    } catch (error) {
      console.error('[Main IFSC Service] Error normalizing Razorpay response:', error)
      return null
    }
  }

  /**
   * Normalize JustinClicks API response
   */
  private normalizeJustinClicksResponse(
    data: JustinClicksResponse,
    ifscCode: string
  ): IFSCDetails | null {
    try {
      if (!data || typeof data !== 'object') return null

      return {
        BANK: data.BANK || 'Unknown',
        IFSC: ifscCode,
        BRANCH: data.BRANCH || 'Unknown',
        ADDRESS: data.ADDRESS || 'Unknown',
        CONTACT: data.CONTACT || '',
        CITY: data.CITY || 'Unknown',
        RTGS: data.RTGS === 'Y' || data.RTGS === true,
        NEFT: data.NEFT === 'Y' || data.NEFT === true,
        IMPS: data.IMPS === 'Y' || data.IMPS === true,
        UPI: data.UPI === 'Y' || data.UPI === true,
        DISTRICT: data.DISTRICT || 'Unknown',
        STATE: data.STATE || 'Unknown',
        BANKCODE: data.BANKCODE || ifscCode.substring(0, 4),
        MICR: data.MICR || undefined
      }
    } catch (error) {
      console.error('[Main IFSC Service] Error normalizing JustinClicks response:', error)
      return null
    }
  }

  /**
   * Cache the result with timestamp
   */
  private cacheResult(ifscCode: string, result: IFSCDetails | null): void {
    try {
      // Implement LRU cache behavior
      if (this.cache.size >= this.maxCacheSize) {
        // Remove oldest entry
        const oldestKey = this.cache.keys().next().value
        if (oldestKey) {
          this.cache.delete(oldestKey)
          this.cacheTimestamps.delete(oldestKey)
        }
      }

      this.cache.set(ifscCode, result)
      this.cacheTimestamps.set(ifscCode, Date.now())

      console.log(
        `[Main IFSC Service] Cached result for ${ifscCode}, cache size: ${this.cache.size}`
      )
    } catch (error) {
      console.error('[Main IFSC Service] Error caching result:', error)
    }
  }

  /**
   * Clear the cache
   */
  public clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    console.log('[Main IFSC Service] Cache cleared')
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number
    maxSize: number
    hitRate: number
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 0 // Could implement hit rate tracking if needed
    }
  }

  /**
   * Batch validate multiple IFSC codes
   */
  public validateMultipleIFSC(ifscCodes: string[]): Record<string, boolean> {
    const results: Record<string, boolean> = {}

    for (const ifscCode of ifscCodes) {
      if (ifscCode) {
        results[ifscCode] = this.validateIFSC(ifscCode)
      }
    }

    return results
  }

  /**
   * Batch fetch multiple IFSC details
   */
  public async fetchMultipleIFSCDetails(
    ifscCodes: string[]
  ): Promise<Record<string, IFSCDetails | null>> {
    const results: Record<string, IFSCDetails | null> = {}

    // Process in parallel but with some rate limiting
    const batchSize = 5
    for (let i = 0; i < ifscCodes.length; i += batchSize) {
      const batch = ifscCodes.slice(i, i + batchSize)
      const batchPromises = batch.map(async (ifscCode) => {
        if (ifscCode) {
          const details = await this.fetchIFSCDetails(ifscCode)
          return { ifscCode, details }
        }
        return { ifscCode, details: null }
      })

      const batchResults = await Promise.all(batchPromises)
      batchResults.forEach(({ ifscCode, details }) => {
        results[ifscCode] = details
      })

      // Small delay between batches to avoid overwhelming the API
      if (i + batchSize < ifscCodes.length) {
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    }

    return results
  }
}

// Export singleton instance
export const mainIFSCService = new MainIFSCService()
export default mainIFSCService
